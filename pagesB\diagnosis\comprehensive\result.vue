<template>
	<view class="comprehensive-result-container">
		<!-- 顶部结果概览 -->
		<view class="result-header">
			<view class="header-content">
				<view class="images-section">
					<view class="image-grid">
						<view class="image-item" v-if="resultData.tongue_image">
							<image :src="resultData.tongue_image" mode="aspectFit"/>
							<text class="image-label">舌诊</text>
						</view>
						<view class="image-item" v-if="resultData.face_image">
							<image :src="resultData.face_image" mode="aspectFit"/>
							<text class="image-label">面诊</text>
						</view>
						<view class="image-item" v-if="resultData.sublingual_image">
							<image :src="resultData.sublingual_image" mode="aspectFit"/>
							<text class="image-label">舌下脉络</text>
						</view>
					</view>
				</view>

				<view class="result-summary">
					<view class="summary-header">
						<text class="summary-title">综合诊疗报告</text>
						<text class="summary-date">{{ currentDate }}</text>
					</view>

					<view class="comprehensive-score" v-if="resultData.comprehensive_score">
						<view class="score-container">
							<view class="score-circle">
								<text class="score-number">{{ resultData.comprehensive_score }}</text>
								<text class="score-label">分</text>
							</view>
							<view class="score-details">
								<text class="score-desc">综合健康评分</text>
								<view class="score-level" :class="scoreLevelClass">
									<text class="level-text">{{ scoreLevelText }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 分析结果概览 -->
		<view class="overview-section">
			<view class="section-header">
				<text class="section-title">分析结果概览</text>
			</view>

			<view class="overview-cards">
				<view class="overview-card" v-if="resultData.tongue_image">
					<view class="card-header">
						<text class="card-icon">👅</text>
						<text class="card-title">舌诊分析</text>
					</view>
					<view class="card-content">
						<text class="card-result">{{ tongueResult.constitution_type || '正常' }}</text>
						<text class="card-score">评分：{{ tongueResult.constitution_score || '85' }}分</text>
					</view>
				</view>

				<view class="overview-card" v-if="resultData.face_image">
					<view class="card-header">
						<text class="card-icon">😊</text>
						<text class="card-title">面诊分析</text>
					</view>
					<view class="card-content">
						<text class="card-result">{{ faceResult.face_status || '气血充足' }}</text>
						<text class="card-score">评分：{{ faceResult.face_score || '82' }}分</text>
					</view>
				</view>

				<view class="overview-card" v-if="resultData.sublingual_image">
					<view class="card-header">
						<text class="card-icon">🩸</text>
						<text class="card-title">舌下脉络</text>
					</view>
					<view class="card-content">
						<text class="card-result">{{ sublingualResult.vein_status || '血液循环良好' }}</text>
						<text class="card-score">评分：{{ sublingualResult.vein_score || '88' }}分</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 详细分析 -->
		<view class="analysis-section">
			<view class="section-header">
				<text class="section-title">详细分析报告</text>
			</view>

			<view class="analysis-tabs">
				<view
					class="tab-item"
					:class="{ active: activeTab === 'comprehensive' }"
					@click="switchTab('comprehensive')"
				>
					<text class="tab-text">综合分析</text>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'individual' }"
					@click="switchTab('individual')"
				>
					<text class="tab-text">单项分析</text>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'advice' }"
					@click="switchTab('advice')"
				>
					<text class="tab-text">调理建议</text>
				</view>
			</view>

			<view class="tab-content-wrapper">
				<!-- 综合分析 -->
				<view v-if="activeTab === 'comprehensive'" class="content-panel comprehensive-panel">
					<view class="comprehensive-analysis">
						<view class="analysis-item">
							<view class="item-header">
								<text class="item-title">整体健康状况</text>
								<view class="item-level normal">
									<text class="level-text">良好</text>
								</view>
							</view>
							<view class="item-content">
								<text class="content-text">{{ comprehensiveAnalysis.overall_health || '根据舌诊、面诊和舌下脉络综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。' }}</text>
							</view>
						</view>

						<view class="analysis-item">
							<view class="item-header">
								<text class="item-title">体质类型</text>
								<view class="item-level normal">
									<text class="level-text">{{ comprehensiveAnalysis.constitution_type || '平和质' }}</text>
								</view>
							</view>
							<view class="item-content">
								<text class="content-text">{{ comprehensiveAnalysis.constitution_desc || '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。' }}</text>
							</view>
						</view>

						<view class="analysis-item">
							<view class="item-header">
								<text class="item-title">健康建议</text>
							</view>
							<view class="item-content">
								<text class="content-text">{{ comprehensiveAnalysis.health_advice || '保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。' }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 单项分析 -->
				<view v-if="activeTab === 'individual'" class="content-panel individual-panel">
					<view class="individual-analysis">
						<view class="individual-item" v-if="resultData.tongue_image">
							<view class="item-title">舌诊分析详情</view>
							<view class="item-details">
								<text class="detail-text">{{ tongueAnalysis.detailed_analysis || '舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。' }}</text>
							</view>
						</view>

						<view class="individual-item" v-if="resultData.face_image">
							<view class="item-title">面诊分析详情</view>
							<view class="item-details">
								<text class="detail-text">{{ faceAnalysis.detailed_analysis || '面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。' }}</text>
							</view>
						</view>

						<view class="individual-item" v-if="resultData.sublingual_image">
							<view class="item-title">舌下脉络分析详情</view>
							<view class="item-details">
								<text class="detail-text">{{ sublingualAnalysis.detailed_analysis || '舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。' }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 调理建议 -->
				<view v-if="activeTab === 'advice'" class="content-panel advice-panel">
					<view class="advice-list">
						<view class="advice-item">
							<view class="advice-header">
								<text class="advice-icon">🍎</text>
								<text class="advice-title">饮食调理</text>
							</view>
							<view class="advice-content">
								<text class="advice-text">{{ careAdvice.diet || '保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。' }}</text>
							</view>
						</view>

						<view class="advice-item">
							<view class="advice-header">
								<text class="advice-icon">💤</text>
								<text class="advice-title">作息调理</text>
							</view>
							<view class="advice-content">
								<text class="advice-text">{{ careAdvice.sleep || '保持规律作息，早睡早起，保证充足睡眠，避免熬夜。' }}</text>
							</view>
						</view>

						<view class="advice-item">
							<view class="advice-header">
								<text class="advice-icon">🏃</text>
								<text class="advice-title">运动调理</text>
							</view>
							<view class="advice-content">
								<text class="advice-text">{{ careAdvice.exercise || '适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。' }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 视频推荐区域 -->
		<view class="video-recommend-section" v-if="recommendVideos.length > 0">
			<view class="section-header">
				<text class="section-title">🎬 体质调理视频推荐</text>
				<text class="section-subtitle">根据您的体质特点，为您推荐专业调理视频</text>
			</view>

			<view class="video-list">
				<view
					class="video-item"
					v-for="(video, index) in recommendVideos"
					:key="video.id"
					@click="playVideo(video)"
				>
					<view class="video-cover">
						<image :src="video.pic" mode="aspectFill" class="cover-image"/>
						<view class="play-icon">
							<text class="play-text">▶</text>
						</view>
						<view class="video-duration" v-if="video.duration">
							<text class="duration-text">{{ video.duration }}</text>
						</view>
					</view>
					<view class="video-info">
						<text class="video-title">{{ video.name }}</text>
						<text class="video-reason">{{ video.recommend_reason }}</text>
						<view class="video-stats">
							<text class="stat-item">👁 {{ video.view_num || 0 }}次观看</text>
							<text class="stat-item">👍 {{ video.zan_num || 0 }}点赞</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<view class="action-btn secondary-btn" @click="goBack">
				<text class="btn-text">返回</text>
			</view>
			<view class="action-btn primary-btn" @click="shareResult" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
				<text class="btn-text">分享报告</text>
			</view>
		</view>

		<!-- 视频播放器遮罩层 -->
		<view class="video-player-overlay" v-if="showVideoPlayer" @click="closeVideoPlayer">
			<view class="video-player-container" @click.stop>
				<view class="video-player-header">
					<text class="video-player-title">{{ currentVideo.name }}</text>
					<view class="close-btn" @click="closeVideoPlayer">
						<text class="close-text">✕</text>
					</view>
				</view>
				<video
					:src="currentVideo.url"
					:poster="currentVideo.pic"
					controls
					autoplay
					playsinline
					webkit-playsinline
					x5-playsinline
					class="video-player"
					@error="onVideoError"
					@play="onVideoPlay"
					@pause="onVideoPause"
					@loadstart="onVideoLoadStart"
					@canplay="onVideoCanPlay"
				></video>
				<view class="video-player-info">
					<text class="video-description">{{ currentVideo.recommend_reason }}</text>
					<view class="video-stats-row">
						<text class="stat-item">👁 {{ currentVideo.view_num || 0 }}次观看</text>
						<text class="stat-item">👍 {{ currentVideo.zan_num || 0 }}点赞</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			recordId: '',
			resultData: {},
			activeTab: 'comprehensive',
			tongueResult: {},
			faceResult: {},
			sublingualResult: {},
			comprehensiveAnalysis: {},
			tongueAnalysis: {},
			faceAnalysis: {},
			sublingualAnalysis: {},
			careAdvice: {},
			recommendVideos: [], // 推荐视频列表
			showVideoPlayer: false, // 是否显示视频播放器
			currentVideo: {} // 当前播放的视频
		}
	},
	computed: {
		currentDate() {
			const now = new Date();
			return now.getFullYear() + '-' +
				   String(now.getMonth() + 1).padStart(2, '0') + '-' +
				   String(now.getDate()).padStart(2, '0');
		},
		scoreLevelClass() {
			if (!this.resultData.comprehensive_score) return 'normal';
			const score = parseInt(this.resultData.comprehensive_score);
			if (score >= 80) return 'excellent';
			if (score >= 60) return 'good';
			if (score >= 40) return 'normal';
			return 'poor';
		},
		scoreLevelText() {
			if (!this.resultData.comprehensive_score) return '正常';
			const score = parseInt(this.resultData.comprehensive_score);
			if (score >= 80) return '优秀';
			if (score >= 60) return '良好';
			if (score >= 40) return '正常';
			return '需要关注';
		}
	},
	onLoad(options) {
		if (options.recordId) {
			this.recordId = options.recordId;
			this.getAnalysisRecord();
		}
	},
	methods: {
		// 获取分析记录 - 2025-07-17 修改为使用新的综合诊疗接口
		getAnalysisRecord() {
			console.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_001] 开始获取综合诊疗分析记录');

			const app = getApp();

			uni.showLoading({
				title: '加载中...'
			});

			// 2025-07-17 使用新的综合诊疗接口获取记录
			app.post('ApiComprehensiveAnalysis/getRecord', {
				id: this.recordId
			}, (response) => {
				uni.hideLoading();
				console.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_002] 获取综合诊疗记录结果:', response);

				if (response && response.code === 1) {
					console.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_003] 获取记录成功，开始解析数据');
					this.resultData = response.data;
					this.parseNewAnalysisResult();
				} else {
					console.error('2025-07-17 ERROR-[comprehensive-result][getAnalysisRecord_004] 获取记录失败:', response?.msg);
					uni.showToast({
						title: response?.msg || '获取记录失败',
						icon: 'none'
					});

					// 降级到模拟数据，确保页面能正常显示
					this.loadMockData();
				}
			}, (error) => {
				uni.hideLoading();
				console.error('2025-07-17 ERROR-[comprehensive-result][getAnalysisRecord_005] 获取记录接口调用失败:', error);

				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});

				// 降级到模拟数据，确保页面能正常显示
				this.loadMockData();
			});
		},

		// 2025-07-17 新增：解析新综合诊疗接口的分析结果
		parseNewAnalysisResult() {
			try {
				console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_001] 开始解析新综合诊疗分析结果');
				console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_002] 原始数据:', this.resultData);

				// 从新接口数据中提取信息
				const analysisResult = this.resultData.analysis_result || {};
				let analysisData = {};

				// 处理 analysis_result 数据
				if (typeof analysisResult === 'string') {
					analysisData = JSON.parse(analysisResult);
				} else if (typeof analysisResult === 'object') {
					analysisData = analysisResult;
				}

				console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_003] 解析后的分析数据:', analysisData);

				// 检查是否有新接口的数据结构
				if (analysisData.raw_report_data) {
					console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_004] 发现新接口数据结构');
					this.parseNewApiData(analysisData.raw_report_data);
				} else if (analysisData.physique_name || analysisData.score) {
					console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_005] 发现API数据结构');
					this.parseNewApiData(analysisData);
				} else {
					console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_006] 使用旧版解析方法');
					this.parseAnalysisResult();
					return;
				}

			} catch (error) {
				console.error('2025-07-17 ERROR-[comprehensive-result][parseNewAnalysisResult_007] 解析新综合诊疗结果失败:', error);
				// 回退到旧版解析方法
				this.parseAnalysisResult();
			}
		},

		// 2025-07-17 新增：解析新API数据
		parseNewApiData(apiData) {
			console.log('2025-07-17 INFO-[comprehensive-result][parseNewApiData_001] 开始解析新API数据:', apiData);

			// 更新基础信息
			if (apiData.score) {
				this.resultData.comprehensive_score = apiData.score;
			}

			// 解析特征数据 - 分类处理舌部、面部、舌下特征
			if (apiData.features && Array.isArray(apiData.features)) {
				this.parseNewComprehensiveFeatures(apiData.features);
			} else {
				this.parseDefaultFeatures();
			}

			// 解析综合健康分析
			this.parseNewComprehensiveAnalysis(apiData);

			// 解析调理建议 - 从 advices 对象中提取
			if (apiData.advices) {
				this.parseNewComprehensiveCareAdvice(apiData.advices);
			} else {
				this.parseDefaultCareAdvice();
			}
		},

		// 2025-07-17 新增：解析新API的综合特征
		parseNewComprehensiveFeatures(features) {
			console.log('2025-07-17 INFO-[comprehensive-result][parseNewComprehensiveFeatures_001] 解析综合特征:', features);

			// 分类处理不同部位的特征
			const tongueFeatures = features.filter(f => f.feature_category === '舌部');
			const faceFeatures = features.filter(f => f.feature_category === '面部');
			const sublingualFeatures = features.filter(f => f.feature_category === '舌下');

			// 解析舌诊结果
			if (this.resultData.tongue_image && tongueFeatures.length > 0) {
				const tongueScore = this.calculateFeatureScore(tongueFeatures);
				this.tongueResult = {
					constitution_type: this.getConstitutionFromFeatures(tongueFeatures),
					constitution_score: tongueScore
				};
				this.tongueAnalysis = {
					detailed_analysis: this.generateFeatureAnalysis(tongueFeatures, '舌诊')
				};
			}

			// 解析面诊结果
			if (this.resultData.face_image && faceFeatures.length > 0) {
				const faceScore = this.calculateFeatureScore(faceFeatures);
				this.faceResult = {
					face_status: this.getFaceStatusFromFeatures(faceFeatures),
					face_score: faceScore
				};
				this.faceAnalysis = {
					detailed_analysis: this.generateFeatureAnalysis(faceFeatures, '面诊')
				};
			}

			// 解析舌下脉络结果
			if (this.resultData.sublingual_image && sublingualFeatures.length > 0) {
				const sublingualScore = this.calculateFeatureScore(sublingualFeatures);
				this.sublingualResult = {
					vein_status: this.getVeinStatusFromFeatures(sublingualFeatures),
					vein_score: sublingualScore
				};
				this.sublingualAnalysis = {
					detailed_analysis: this.generateFeatureAnalysis(sublingualFeatures, '舌下脉络')
				};
			}

			// 获取推荐视频
			this.getRecommendVideos();
		},

		// 2025-07-17 新增：解析新API的综合健康分析
		parseNewComprehensiveAnalysis(apiData) {
			const score = apiData.score || 85;
			const physiqueName = apiData.physique_name || '未知体质';
			const physiqueAnalysis = apiData.physique_analysis || '体质分析中...';
			const riskWarning = apiData.risk_warning || '请注意保持健康的生活方式';

			this.comprehensiveAnalysis = {
				overall_health: physiqueAnalysis,
				constitution_type: physiqueName,
				constitution_desc: this.getConstitutionDescription(physiqueName),
				health_advice: riskWarning
			};
		},

		// 2025-07-17 新增：解析新API的综合调理建议
		parseNewComprehensiveCareAdvice(advices) {
			console.log('2025-07-17 INFO-[comprehensive-result][parseNewComprehensiveCareAdvice_001] 解析综合调理建议:', advices);

			// 饮食建议
			let dietAdvice = '保持均衡饮食，多吃新鲜蔬果';
			if (advices.food && Array.isArray(advices.food)) {
				dietAdvice = advices.food.map(item => item.advice || item.title).join('；');
			}

			// 运动建议
			let exerciseAdvice = '适当进行有氧运动，增强体质';
			if (advices.sport && Array.isArray(advices.sport)) {
				exerciseAdvice = advices.sport.map(item => item.advice || item.title).join('；');
			}

			// 生活建议
			let sleepAdvice = '保持规律作息，早睡早起';
			if (advices.sleep && Array.isArray(advices.sleep)) {
				sleepAdvice = advices.sleep.map(item => item.advice || item.title).join('；');
			}

			this.careAdvice = {
				diet: dietAdvice,
				exercise: exerciseAdvice,
				sleep: sleepAdvice
			};
		},

		// 2025-07-17 新增：计算特征评分
		calculateFeatureScore(features) {
			if (!features || features.length === 0) return 85;

			let normalCount = 0;
			features.forEach(feature => {
				if (feature.feature_situation === '正常') {
					normalCount++;
				}
			});

			const normalRatio = normalCount / features.length;
			return Math.round(60 + normalRatio * 40); // 60-100分范围
		},

		// 2025-07-17 新增：从特征获取体质类型
		getConstitutionFromFeatures(features) {
			// 简单的体质判断逻辑，可以根据实际需要完善
			const abnormalFeatures = features.filter(f => f.feature_situation === '异常');
			if (abnormalFeatures.length === 0) return '平和质';
			if (abnormalFeatures.length <= 2) return '偏颇体质';
			return '需要调理';
		},

		// 2025-07-17 新增：从面部特征获取面部状态
		getFaceStatusFromFeatures(features) {
			const abnormalFeatures = features.filter(f => f.feature_situation === '异常');
			if (abnormalFeatures.length === 0) return '气血充足';
			if (abnormalFeatures.length <= 1) return '气血较好';
			return '需要调理';
		},

		// 2025-07-17 新增：从舌下特征获取脉络状态
		getVeinStatusFromFeatures(features) {
			const abnormalFeatures = features.filter(f => f.feature_situation === '异常');
			if (abnormalFeatures.length === 0) return '血液循环良好';
			if (abnormalFeatures.length <= 1) return '血液循环较好';
			return '血液循环需要改善';
		},

		// 2025-07-17 新增：生成特征分析文本
		generateFeatureAnalysis(features, type) {
			const normalFeatures = features.filter(f => f.feature_situation === '正常');
			const abnormalFeatures = features.filter(f => f.feature_situation === '异常');

			let analysis = `${type}显示：`;

			if (normalFeatures.length > 0) {
				const normalNames = normalFeatures.map(f => f.feature_name).join('、');
				analysis += `${normalNames}正常；`;
			}

			if (abnormalFeatures.length > 0) {
				const abnormalNames = abnormalFeatures.map(f => f.feature_name).join('、');
				analysis += `${abnormalNames}需要关注。`;
			} else {
				analysis += '整体状况良好。';
			}

			return analysis;
		},

		// 2025-07-17 新增：获取体质描述
		getConstitutionDescription(constitutionType) {
			const descriptions = {
				'平和质': '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',
				'气虚质': '元气不足，以疲乏、气短、自汗等气虚表现为主要特征。',
				'阳虚质': '阳气不足，以畏寒怕冷、手足不温等虚寒表现为主要特征。',
				'阴虚质': '阴液亏少，以口燥咽干、手足心热等虚热表现为主要特征。',
				'痰湿质': '痰湿凝聚，以形体肥胖、腹部肥满、口黏苔腻等痰湿表现为主要特征。',
				'湿热质': '湿热内蕴，以面垢油腻、口苦、苔黄腻等湿热表现为主要特征。',
				'血瘀质': '血行不畅，以肤色晦黯、舌质紫黯等血瘀表现为主要特征。',
				'气郁质': '气机郁滞，以神情抑郁、忧虑脆弱等气郁表现为主要特征。',
				'特禀质': '先天失常，以生理缺陷、过敏反应等为主要特征。'
			};
			return descriptions[constitutionType] || '体质特征分析中，请咨询专业医师。';
		},

		// 2025-07-17 新增：加载模拟数据
		loadMockData() {
			this.resultData = {
				comprehensive_score: 85,
				tongue_image: this.resultData?.tongue_image || '',
				face_image: this.resultData?.face_image || '',
				sublingual_image: this.resultData?.sublingual_image || ''
			};

			this.parseDefaultFeatures();
			this.parseDefaultCareAdvice();
		},

		// 2025-07-17 新增：解析默认特征
		parseDefaultFeatures() {
			if (this.resultData.tongue_image) {
				this.tongueResult = {
					constitution_type: '平和质',
					constitution_score: 85
				};
				this.tongueAnalysis = {
					detailed_analysis: '舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。'
				};
			}

			if (this.resultData.face_image) {
				this.faceResult = {
					face_status: '气血充足',
					face_score: 82
				};
				this.faceAnalysis = {
					detailed_analysis: '面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。'
				};
			}

			if (this.resultData.sublingual_image) {
				this.sublingualResult = {
					vein_status: '血液循环良好',
					vein_score: 88
				};
				this.sublingualAnalysis = {
					detailed_analysis: '舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。'
				};
			}

			this.comprehensiveAnalysis = {
				overall_health: '根据综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。',
				constitution_type: '平和质',
				constitution_desc: '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',
				health_advice: '保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。'
			};
		},

		// 2025-07-17 新增：解析默认调理建议
		parseDefaultCareAdvice() {
			this.careAdvice = {
				diet: '保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。',
				sleep: '保持规律作息，早睡早起，保证充足睡眠，避免熬夜。',
				exercise: '适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。'
			};
		},

		// 解析分析结果（保留旧版本兼容）
		parseAnalysisResult() {
			try {
				// 解析主要分析结果
				const analysisData = JSON.parse(this.resultData.analysis_result || '{}');

				// 解析舌诊结果
				if (this.resultData.tongue_image) {
					this.tongueResult = {
						constitution_type: analysisData.constitution_type || '平和质',
						constitution_score: analysisData.constitution_score || 85
					};
					this.tongueAnalysis = {
						detailed_analysis: analysisData.tongue_detailed || '舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。'
					};
				}

				// 解析面诊结果
				if (this.resultData.face_image) {
					this.faceResult = {
						face_status: analysisData.face_status || '气血充足',
						face_score: analysisData.face_score || 82
					};
					this.faceAnalysis = {
						detailed_analysis: analysisData.face_detailed || '面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。'
					};
				}

				// 解析舌下脉络结果
				if (this.resultData.sublingual_image) {
					this.sublingualResult = {
						vein_status: analysisData.vein_status || '血液循环良好',
						vein_score: analysisData.vein_score || 88
					};
					this.sublingualAnalysis = {
						detailed_analysis: analysisData.sublingual_detailed || '舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。'
					};
				}

				// 解析综合分析
				this.comprehensiveAnalysis = {
					overall_health: analysisData.overall_health || '根据舌诊、面诊和舌下脉络综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。',
					constitution_type: analysisData.comprehensive_constitution || '平和质',
					constitution_desc: analysisData.comprehensive_desc || '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',
					health_advice: analysisData.comprehensive_advice || '保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。'
				};

				// 解析调理建议
				this.careAdvice = {
					diet: analysisData.diet_advice || '保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。',
					sleep: analysisData.sleep_advice || '保持规律作息，早睡早起，保证充足睡眠，避免熬夜。',
					exercise: analysisData.exercise_advice || '适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。'
				};

			} catch (error) {
				console.error('解析分析结果失败:', error);
			}
		},

		// 切换标签
		switchTab(tab) {
			this.activeTab = tab;
		},

		// 返回
		goBack() {
			uni.navigateBack();
		},

		// 分享结果
		shareResult() {
			uni.showToast({
				title: '分享功能开发中',
				icon: 'none'
			});
		},

		// 获取推荐视频
		getRecommendVideos() {
			console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_001] 开始获取推荐视频');

			// 获取体质类型和得分
			let constitutionType = '';
			let constitutionScore = 0;

			if (this.tongueResult && this.tongueResult.constitution_type) {
				constitutionType = this.tongueResult.constitution_type;
			}
			if (this.tongueResult && this.tongueResult.constitution_score) {
				constitutionScore = parseInt(this.tongueResult.constitution_score);
			}

			// 如果没有体质信息，不获取推荐视频
			if (!constitutionType && !constitutionScore) {
				console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_002] 无体质信息，跳过视频推荐');
				return;
			}

			const app = getApp();

			// 调用推荐视频接口
			app.post('ApiSheZhen/getRecommendProducts', {
				constitution_type: constitutionType,
				constitution_score: constitutionScore
			}, (response) => {
				console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_003] 获取推荐视频结果:', response);

				if (response && response.code === 1 && response.data && response.data.products) {
					// 筛选出视频类型的推荐
					const videos = response.data.products.filter(item => item.type === 'video');
					this.recommendVideos = videos;
					console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_004] 获取到推荐视频数量:', videos.length);
				} else {
					console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_005] 无推荐视频数据');
				}
			}, (error) => {
				console.error('2025-01-31 ERROR-[comprehensive-result][getRecommendVideos_006] 获取推荐视频失败:', error);
			});
		},

		// 播放视频
		playVideo(video) {
			console.log('2025-01-31 INFO-[comprehensive-result][playVideo_001] 播放视频:', video);

			if (!video.url) {
				uni.showToast({
					title: '视频地址无效',
					icon: 'none'
				});
				return;
			}

			// 处理视频URL，确保中文字符正确编码
			let processedVideo = { ...video };
			if (video.url && video.url.includes('%')) {
				// URL已经编码，直接使用
				processedVideo.url = video.url;
			} else if (video.url) {
				// 对URL中的中文字符进行编码
				try {
					const urlParts = video.url.split('/');
					const encodedParts = urlParts.map(part => {
						// 只对文件名部分进行编码，保留协议和域名
						if (part.includes('.') && (part.includes('mp4') || part.includes('avi') || part.includes('mov'))) {
							return encodeURIComponent(part);
						}
						return part;
					});
					processedVideo.url = encodedParts.join('/');
					console.log('2025-01-31 INFO-[comprehensive-result][playVideo_001.5] URL编码处理:', {
						original: video.url,
						processed: processedVideo.url
					});
				} catch (e) {
					console.warn('2025-01-31 WARN-[comprehensive-result][playVideo_001.6] URL编码失败，使用原始URL:', e);
					processedVideo.url = video.url;
				}
			}

			// 设置当前播放视频并显示播放器
			this.currentVideo = processedVideo;
			this.showVideoPlayer = true;

			// 记录视频播放事件
			console.log('2025-01-31 INFO-[comprehensive-result][playVideo_002] 开始播放视频:', video.name);
			console.log('2025-01-31 INFO-[comprehensive-result][playVideo_003] 视频URL:', processedVideo.url);
		},

		// 关闭视频播放器
		closeVideoPlayer() {
			console.log('2025-01-31 INFO-[comprehensive-result][closeVideoPlayer_001] 关闭视频播放器');
			this.showVideoPlayer = false;
			this.currentVideo = {};
		},

		// 视频播放错误处理
		onVideoError(e) {
			console.error('2025-01-31 ERROR-[comprehensive-result][onVideoError_001] 视频播放错误:', e);
			uni.showToast({
				title: '视频播放失败',
				icon: 'none'
			});
		},

		// 视频开始播放
		onVideoPlay(e) {
			console.log('2025-01-31 INFO-[comprehensive-result][onVideoPlay_001] 视频开始播放:', e);
		},

		// 视频暂停播放
		onVideoPause(e) {
			console.log('2025-01-31 INFO-[comprehensive-result][onVideoPause_001] 视频暂停播放:', e);
		},

		// 视频开始加载
		onVideoLoadStart(e) {
			console.log('2025-01-31 INFO-[comprehensive-result][onVideoLoadStart_001] 视频开始加载:', e);
		},

		// 视频可以播放
		onVideoCanPlay(e) {
			console.log('2025-01-31 INFO-[comprehensive-result][onVideoCanPlay_001] 视频可以播放:', e);
		}
	}
}
</script>

<style>
.comprehensive-result-container {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 120rpx;
}

.result-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 30rpx;
	color: #fff;
}

.header-content {
	display: flex;
	flex-direction: column;
}

.images-section {
	margin-bottom: 30rpx;
}

.image-grid {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}

.image-item {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	overflow: hidden;
	background: rgba(255,255,255,0.1);
}

.image-item image {
	width: 100%;
	height: 100%;
}

.image-label {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0,0,0,0.7);
	color: #fff;
	font-size: 20rpx;
	text-align: center;
	padding: 6rpx;
}

.result-summary {
	text-align: center;
}

.summary-title {
	font-size: 36rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}

.summary-date {
	font-size: 26rpx;
	opacity: 0.8;
}

.comprehensive-score {
	margin-top: 30rpx;
}

.score-container {
	display: flex;
	align-items: center;
	justify-content: center;
}

.score-circle {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255,255,255,0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
}

.score-number {
	font-size: 36rpx;
	font-weight: 600;
	line-height: 1;
}

.score-label {
	font-size: 22rpx;
	opacity: 0.8;
}

.score-details {
	text-align: left;
}

.score-desc {
	font-size: 28rpx;
	display: block;
	margin-bottom: 15rpx;
}

.score-level {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	display: inline-block;
}

.score-level.excellent {
	background: rgba(76, 175, 80, 0.3);
}

.score-level.good {
	background: rgba(139, 195, 74, 0.3);
}

.score-level.normal {
	background: rgba(255, 193, 7, 0.3);
}

.score-level.poor {
	background: rgba(244, 67, 54, 0.3);
}

.level-text {
	font-size: 26rpx;
	color: #fff;
}

.overview-section, .analysis-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}

.section-header {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.overview-cards {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.overview-card {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.card-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
}

.card-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.card-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.card-result {
	font-size: 26rpx;
	color: #007aff;
	font-weight: 500;
}

.card-score {
	font-size: 24rpx;
	color: #666;
}

.analysis-tabs {
	display: flex;
	background: #f5f5f5;
	border-radius: 15rpx;
	padding: 8rpx;
	margin-bottom: 30rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 10rpx;
	transition: all 0.3s;
}

.tab-item.active {
	background: #007aff;
	color: #fff;
}

.tab-text {
	font-size: 26rpx;
	font-weight: 500;
}

.content-panel {
	min-height: 300rpx;
}

.comprehensive-analysis, .individual-analysis, .advice-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.analysis-item, .individual-item, .advice-item {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
}

.item-header, .advice-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.item-title, .advice-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.item-level {
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.item-level.normal {
	background: #e8f5e8;
	color: #4caf50;
}

.item-content, .item-details, .advice-content {
	margin-top: 15rpx;
}

.content-text, .detail-text, .advice-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.advice-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 30rpx;
	border-top: 1px solid #eee;
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.secondary-btn {
	background: #f5f5f5;
	color: #666;
}

.primary-btn {
	color: #fff;
}

.btn-text {
	font-size: 32rpx;
	font-weight: 600;
}

/* 视频推荐区域样式 */
.video-recommend-section {
	background: #fff;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
}

.section-subtitle {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.video-list {
	margin-top: 30rpx;
}

.video-item {
	display: flex;
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	border: 1px solid #eee;
}

.video-item:last-child {
	margin-bottom: 0;
}

.video-cover {
	position: relative;
	width: 200rpx;
	height: 120rpx;
	border-radius: 10rpx;
	overflow: hidden;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.cover-image {
	width: 100%;
	height: 100%;
}

.play-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 60rpx;
	height: 60rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.play-text {
	color: #fff;
	font-size: 24rpx;
	margin-left: 4rpx;
}

.video-duration {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	background: rgba(0, 0, 0, 0.7);
	color: #fff;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
}

.duration-text {
	font-size: 20rpx;
}

.video-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.video-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	line-height: 1.4;
	margin-bottom: 10rpx;
}

.video-reason {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
	padding: 8rpx 12rpx;
	background: #e8f5e8;
	color: #4caf50;
	border-radius: 8rpx;
	align-self: flex-start;
}

.video-stats {
	display: flex;
	gap: 20rpx;
}

.stat-item {
	font-size: 22rpx;
	color: #999;
}

/* 视频播放器遮罩层样式 */
.video-player-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.video-player-container {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	width: 100%;
	max-width: 640rpx;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.video-player-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background: #f8f9fa;
	border-bottom: 1px solid #eee;
}

.video-player-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	flex: 1;
	margin-right: 20rpx;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f0f0f0;
	border-radius: 50%;
	cursor: pointer;
}

.close-text {
	font-size: 32rpx;
	color: #666;
	font-weight: bold;
}

.video-player {
	width: 100%;
	height: 400rpx;
	background: #000;
}

.video-player-info {
	padding: 20rpx 30rpx;
	background: #fff;
}

.video-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
	display: block;
}

.video-stats-row {
	display: flex;
	gap: 20rpx;
}

.video-stats-row .stat-item {
	font-size: 24rpx;
	color: #999;
}
</style>