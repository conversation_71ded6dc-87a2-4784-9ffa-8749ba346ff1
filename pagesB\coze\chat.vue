<template>
<view class="container">
	<block v-if="isload">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="header-title">扣子AI聊天</view>
			<view class="header-actions">
				<view class="action-btn" @tap="showBotList">
					<text class="iconfont iconjiqiren"></text>
					<text>选择机器人</text>
				</view>
			</view>
		</view>

		<!-- 机器人信息 -->
		<view class="bot-info" v-if="currentBot.bot_id">
			<image class="bot-avatar" :src="currentBot.icon_url || '/static/img/default-bot.png'"></image>
			<view class="bot-details">
				<view class="bot-name">{{currentBot.name}}</view>
				<view class="bot-desc">{{currentBot.description}}</view>
			</view>
		</view>

		<!-- 聊天消息列表 -->
		<scroll-view class="chat-messages" scroll-y="true" :scroll-top="scrollTop" scroll-with-animation="true">
			<view class="message-list">
				<block v-for="(message, index) in messageList" :key="index">
					<view class="message-item" :class="message.role === 'user' ? 'user-message' : 'bot-message'">
						<view class="message-avatar">
							<image v-if="message.role === 'user'" :src="userInfo.avatar || '/static/img/default-user.png'"></image>
							<image v-else :src="currentBot.icon_url || '/static/img/default-bot.png'"></image>
						</view>
						<view class="message-content">
							<view class="message-text">{{message.content}}</view>
							<view class="message-time">{{message.create_time_text}}</view>
						</view>
					</view>
				</block>
				<view v-if="isTyping" class="message-item bot-message">
					<view class="message-avatar">
						<image :src="currentBot.icon_url || '/static/img/default-bot.png'"></image>
					</view>
					<view class="message-content">
						<view class="typing-indicator">
							<view class="typing-dot"></view>
							<view class="typing-dot"></view>
							<view class="typing-dot"></view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 输入框 -->
		<view class="input-area">
			<view class="input-container">
				<view class="input-box">
					<input 
						v-model="inputMessage" 
						placeholder="请输入消息..." 
						placeholder-style="color:#999"
						@confirm="sendMessage"
						confirm-type="send"
						:disabled="isTyping"
					/>
				</view>
				<view class="send-btn" :class="inputMessage.trim() ? 'active' : ''" @tap="sendMessage">
					<text class="iconfont iconfasong"></text>
				</view>
			</view>
		</view>

		<!-- 机器人选择弹窗 -->
		<uni-popup ref="botListPopup" type="bottom">
			<view class="bot-list-popup">
				<view class="popup-header">
					<view class="popup-title">选择机器人</view>
					<view class="popup-close" @tap="closeBotList">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				<scroll-view class="bot-list" scroll-y="true">
					<block v-for="(bot, index) in botList" :key="index">
						<view class="bot-item" @tap="selectBot" :data-bot="JSON.stringify(bot)">
							<image class="bot-item-avatar" :src="bot.icon_url || '/static/img/default-bot.png'"></image>
							<view class="bot-item-info">
								<view class="bot-item-name">{{bot.name}}</view>
								<view class="bot-item-desc">{{bot.description}}</view>
							</view>
							<view class="bot-item-check" v-if="currentBot.bot_id === bot.bot_id">
								<text class="iconfont iconduihao" :style="{color:t('color1')}"></text>
							</view>
						</view>
					</block>
				</scroll-view>
			</view>
		</uni-popup>
	</block>
	<loading v-if="loading"></loading>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			isload: false,
			loading: false,
			userInfo: {},
			currentBot: {},
			botList: [],
			messageList: [],
			inputMessage: '',
			conversationId: '',
			isTyping: false,
			scrollTop: 0,
			pagenum: 1,
			nomore: false
		};
	},

	onLoad: function(opt) {
		this.userInfo = app.getUserInfo();
		this.getBotList();
	},

	onShow: function() {
		this.scrollToBottom();
	},

	methods: {
		// 获取机器人列表
		getBotList: function() {
			var that = this;
			that.loading = true;
			app.post('ApiCoze/getbotlist', {}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					that.botList = res.data || [];
					if (that.botList.length > 0 && !that.currentBot.bot_id) {
						that.currentBot = that.botList[0];
						that.getConversationHistory();
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
				that.loaded();
			});
		},

		// 显示机器人列表
		showBotList: function() {
			this.$refs.botListPopup.open();
		},

		// 关闭机器人列表
		closeBotList: function() {
			this.$refs.botListPopup.close();
		},

		// 选择机器人
		selectBot: function(e) {
			var bot = JSON.parse(e.currentTarget.dataset.bot);
			this.currentBot = bot;
			this.messageList = [];
			this.conversationId = '';
			this.getConversationHistory();
			this.closeBotList();
		},

		// 获取对话历史
		getConversationHistory: function() {
			if (!this.currentBot.bot_id) return;
			
			var that = this;
			app.post('ApiCoze/getconversations', {
				pagenum: 1,
				pagesize: 1
			}, function(res) {
				if (res.code === 1 && res.data.length > 0) {
					that.conversationId = res.data[0].conversation_id;
					that.getMessages();
				}
			});
		},

		// 获取消息列表
		getMessages: function() {
			if (!this.conversationId) return;
			
			var that = this;
			app.post('ApiCoze/getmessages', {
				conversation_id: that.conversationId,
				pagenum: that.pagenum,
				pagesize: 50
			}, function(res) {
				if (res.code === 1) {
					that.messageList = res.data || [];
					that.scrollToBottom();
				}
			});
		},

		// 发送消息
		sendMessage: function() {
			if (!this.inputMessage.trim() || this.isTyping) return;
			if (!this.currentBot.bot_id) {
				uni.showToast({
					title: '请先选择机器人',
					icon: 'none',
					duration: 2000
				});
				return;
			}

			var message = this.inputMessage.trim();
			this.inputMessage = '';

			// 添加用户消息到列表
			this.messageList.push({
				role: 'user',
				content: message,
				create_time_text: this.formatTime(new Date())
			});

			this.isTyping = true;
			this.scrollToBottom();

			var that = this;
			app.post('ApiCoze/chat', {
				bot_id: that.currentBot.bot_id,
				message: message,
				conversation_id: that.conversationId
			}, function(res) {
				that.isTyping = false;
				if (res.code === 1) {
					// 添加机器人回复到列表
					that.messageList.push({
						role: 'assistant',
						content: res.data.content || res.msg,
						create_time_text: that.formatTime(new Date())
					});
					
					if (res.data.conversation_id) {
						that.conversationId = res.data.conversation_id;
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
				that.scrollToBottom();
			});
		},

		// 滚动到底部
		scrollToBottom: function() {
			this.$nextTick(() => {
				this.scrollTop = 999999;
			});
		},

		// 格式化时间
		formatTime: function(date) {
			var now = new Date();
			var diff = now - date;
			
			if (diff < 60000) { // 1分钟内
				return '刚刚';
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前';
			} else if (diff < 86400000) { // 24小时内
				return Math.floor(diff / 3600000) + '小时前';
			} else {
				return date.getFullYear() + '-' + 
					   (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
					   date.getDate().toString().padStart(2, '0') + ' ' +
					   date.getHours().toString().padStart(2, '0') + ':' + 
					   date.getMinutes().toString().padStart(2, '0');
			}
		}
	}
};
</script>

<style>
.container {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
}

.header {
	background: #fff;
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #eee;
	position: fixed;
	top: var(--window-top);
	left: 0;
	right: 0;
	z-index: 100;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.header-actions {
	display: flex;
	align-items: center;
}

.action-btn {
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	background: #f0f0f0;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #666;
}

.action-btn .iconfont {
	margin-right: 10rpx;
	font-size: 28rpx;
}

.bot-info {
	background: #fff;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	border-bottom: 1rpx solid #eee;
	margin-top: 120rpx;
}

.bot-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
}

.bot-details {
	flex: 1;
}

.bot-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.bot-desc {
	font-size: 24rpx;
	color: #999;
}

.chat-messages {
	flex: 1;
	padding: 20rpx 30rpx;
}

.message-item {
	display: flex;
	margin-bottom: 30rpx;
}

.user-message {
	flex-direction: row-reverse;
}

.message-avatar {
	width: 60rpx;
	height: 60rpx;
	margin: 0 20rpx;
}

.message-avatar image {
	width: 100%;
	height: 100%;
	border-radius: 30rpx;
}

.message-content {
	max-width: 70%;
}

.user-message .message-content {
	text-align: right;
}

.message-text {
	background: #fff;
	padding: 20rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #333;
	word-wrap: break-word;
}

.user-message .message-text {
	background: #007aff;
	color: #fff;
}

.message-time {
	font-size: 20rpx;
	color: #999;
	margin-top: 10rpx;
}

.typing-indicator {
	background: #fff;
	padding: 20rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
}

.typing-dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: #999;
	margin-right: 8rpx;
	animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
	0%, 80%, 100% { transform: scale(0); }
	40% { transform: scale(1); }
}

.input-area {
	background: #fff;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #eee;
}

.input-container {
	display: flex;
	align-items: center;
}

.input-box {
	flex: 1;
	background: #f5f5f5;
	border-radius: 30rpx;
	padding: 0 30rpx;
	margin-right: 20rpx;
}

.input-box input {
	height: 60rpx;
	font-size: 28rpx;
	color: #333;
}

.send-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	background: #ccc;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 28rpx;
}

.send-btn.active {
	background: #007aff;
}

.bot-list-popup {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.popup-header {
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 32rpx;
	color: #999;
}

.bot-list {
	flex: 1;
	padding: 0 30rpx;
}

.bot-item {
	display: flex;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.bot-item-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
}

.bot-item-info {
	flex: 1;
}

.bot-item-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.bot-item-desc {
	font-size: 24rpx;
	color: #999;
}

.bot-item-check {
	font-size: 32rpx;
}
</style>
