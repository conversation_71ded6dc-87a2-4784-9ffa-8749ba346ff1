<template>
<view class="container">
	<block v-if="isload">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="header-title">工作流执行记录</view>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-bar">
			<view class="filter-item" @tap="showWorkflowFilter">
				<text>{{selectedWorkflow ? selectedWorkflow.name : '全部工作流'}}</text>
				<text class="iconfont iconxiala"></text>
			</view>
		</view>

		<!-- 执行记录列表 -->
		<view class="logs-list">
			<block v-for="(log, index) in logsList" :key="index">
				<view class="log-item" @tap="showLogDetail" :data-log="JSON.stringify(log)">
					<view class="log-header">
						<view class="log-workflow">{{log.workflow_name || log.workflow_id}}</view>
						<view class="log-status" :class="log.status ? 'success' : 'failed'">
							<text class="iconfont" :class="log.status ? 'iconzhengchang' : 'iconcuowu'"></text>
							<text>{{log.status ? '成功' : '失败'}}</text>
						</view>
					</view>
					<view class="log-time">{{log.create_time_text}}</view>
					<view class="log-params" v-if="log.parameters_preview">
						<text class="label">参数：</text>
						<text class="value">{{log.parameters_preview}}</text>
					</view>
				</view>
			</block>
			<nomore v-if="nomore"></nomore>
			<nodata v-if="nodata"></nodata>
		</view>

		<!-- 工作流筛选弹窗 -->
		<uni-popup ref="workflowFilterPopup" type="bottom">
			<view class="filter-popup">
				<view class="popup-header">
					<view class="popup-title">选择工作流</view>
					<view class="popup-close" @tap="closeWorkflowFilter">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				<scroll-view class="filter-options" scroll-y="true">
					<view class="filter-option" @tap="selectWorkflowFilter" data-workflow="">
						<text>全部工作流</text>
						<text v-if="!selectedWorkflow" class="iconfont iconduihao" :style="{color: t('color1')}"></text>
					</view>
					<block v-for="(workflow, index) in workflowList" :key="index">
						<view class="filter-option" @tap="selectWorkflowFilter" :data-workflow="JSON.stringify(workflow)">
							<text>{{workflow.name}}</text>
							<text v-if="selectedWorkflow && selectedWorkflow.workflow_id === workflow.workflow_id" class="iconfont iconduihao" :style="{color: t('color1')}"></text>
						</view>
					</block>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 日志详情弹窗 -->
		<uni-popup ref="logDetailPopup" type="center">
			<view class="detail-popup">
				<view class="popup-header">
					<view class="popup-title">执行详情</view>
					<view class="popup-close" @tap="closeLogDetail">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				<scroll-view class="detail-content" scroll-y="true">
					<view class="detail-section">
						<view class="section-title">工作流信息</view>
						<view class="detail-item">
							<text class="label">工作流ID：</text>
							<text class="value">{{currentLog.workflow_id}}</text>
						</view>
						<view class="detail-item">
							<text class="label">执行状态：</text>
							<text class="value" :class="currentLog.status ? 'success' : 'failed'">
								{{currentLog.status ? '成功' : '失败'}}
							</text>
						</view>
						<view class="detail-item">
							<text class="label">执行时间：</text>
							<text class="value">{{currentLog.create_time_text}}</text>
						</view>
					</view>

					<view class="detail-section" v-if="currentLog.parameters">
						<view class="section-title">输入参数</view>
						<view class="code-block">
							<text>{{formatJson(currentLog.parameters)}}</text>
						</view>
					</view>

					<view class="detail-section" v-if="currentLog.result">
						<view class="section-title">执行结果</view>
						<view class="code-block">
							<text>{{formatJson(currentLog.result)}}</text>
						</view>
					</view>
				</scroll-view>
				<view class="popup-footer">
					<view class="btn-confirm" :style="{background: t('color1')}" @tap="closeLogDetail">确定</view>
				</view>
			</view>
		</uni-popup>
	</block>
	<loading v-if="loading"></loading>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			isload: false,
			loading: false,
			logsList: [],
			workflowList: [],
			selectedWorkflow: null,
			currentLog: {},
			pagenum: 1,
			nomore: false,
			nodata: false
		};
	},

	onLoad: function(opt) {
		this.getWorkflowList();
		this.getLogsList();
	},

	onReachBottom: function() {
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getLogsList(true);
		}
	},

	onPullDownRefresh: function() {
		this.getLogsList();
	},

	methods: {
		// 获取工作流列表
		getWorkflowList: function() {
			var that = this;
			app.post('ApiCoze/getWorkflowList', {}, function(res) {
				if (res.code === 1) {
					that.workflowList = res.data || [];
				}
			});
		},

		// 获取执行记录列表
		getLogsList: function(loadmore) {
			if (!loadmore) {
				this.pagenum = 1;
				this.logsList = [];
			}

			var that = this;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;

			var params = {
				page: that.pagenum,
				limit: 20
			};

			if (that.selectedWorkflow) {
				params.workflow_id = that.selectedWorkflow.workflow_id;
			}

			app.post('ApiCoze/getWorkflowLogs', params, function(res) {
				that.loading = false;
				uni.stopPullDownRefresh();

				if (res.code === 1) {
					var data = res.data.list || [];
					
					// 处理数据
					data.forEach(function(item) {
						item.create_time_text = that.formatTime(item.create_time);
						
						// 处理参数预览
						if (item.parameters) {
							try {
								var params = JSON.parse(item.parameters);
								var keys = Object.keys(params);
								if (keys.length > 0) {
									item.parameters_preview = keys.slice(0, 2).map(key => key + ': ' + params[key]).join(', ');
									if (keys.length > 2) {
										item.parameters_preview += '...';
									}
								}
							} catch (e) {
								item.parameters_preview = '参数解析失败';
							}
						}
					});

					if (that.pagenum === 1) {
						that.logsList = data;
						if (data.length === 0) {
							that.nodata = true;
						}
					} else {
						if (data.length === 0) {
							that.nomore = true;
						} else {
							that.logsList = that.logsList.concat(data);
						}
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
					if (that.pagenum === 1) {
						that.nodata = true;
					}
				}
				that.loaded();
			});
		},

		// 显示工作流筛选
		showWorkflowFilter: function() {
			this.$refs.workflowFilterPopup.open();
		},

		// 关闭工作流筛选
		closeWorkflowFilter: function() {
			this.$refs.workflowFilterPopup.close();
		},

		// 选择工作流筛选
		selectWorkflowFilter: function(e) {
			var workflowData = e.currentTarget.dataset.workflow;
			if (workflowData) {
				this.selectedWorkflow = JSON.parse(workflowData);
			} else {
				this.selectedWorkflow = null;
			}
			this.getLogsList();
			this.closeWorkflowFilter();
		},

		// 显示日志详情
		showLogDetail: function(e) {
			var log = JSON.parse(e.currentTarget.dataset.log);
			this.currentLog = log;
			this.$refs.logDetailPopup.open();
		},

		// 关闭日志详情
		closeLogDetail: function() {
			this.$refs.logDetailPopup.close();
		},

		// 格式化JSON
		formatJson: function(jsonStr) {
			try {
				var obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;
				return JSON.stringify(obj, null, 2);
			} catch (e) {
				return jsonStr;
			}
		},

		// 格式化时间
		formatTime: function(timestamp) {
			var date = new Date(timestamp * 1000);
			return date.getFullYear() + '-' + 
				   (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
				   date.getDate().toString().padStart(2, '0') + ' ' +
				   date.getHours().toString().padStart(2, '0') + ':' + 
				   date.getMinutes().toString().padStart(2, '0');
		}
	}
};
</script>

<style>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	background: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #eee;
	position: fixed;
	top: var(--window-top);
	left: 0;
	right: 0;
	z-index: 100;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
}

.filter-bar {
	background: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #eee;
	margin-top: 120rpx;
}

.filter-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #333;
}

.logs-list {
	padding: 30rpx;
}

.log-item {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.log-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.log-workflow {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.log-status {
	display: flex;
	align-items: center;
	font-size: 24rpx;
}

.log-status.success {
	color: #52c41a;
}

.log-status.failed {
	color: #ff4d4f;
}

.log-status .iconfont {
	margin-right: 8rpx;
	font-size: 28rpx;
}

.log-time {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 15rpx;
}

.log-params {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.log-params .label {
	color: #999;
}

.log-params .value {
	color: #333;
}

.filter-popup {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 60vh;
	display: flex;
	flex-direction: column;
}

.popup-header {
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 32rpx;
	color: #999;
}

.filter-options {
	flex: 1;
	padding: 0 30rpx;
}

.filter-option {
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
	color: #333;
}

.detail-popup {
	background: #fff;
	border-radius: 20rpx;
	width: 90vw;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.detail-content {
	flex: 1;
	padding: 30rpx;
	max-height: 60vh;
}

.detail-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	padding-bottom: 10rpx;
	border-bottom: 1rpx solid #eee;
}

.detail-item {
	display: flex;
	margin-bottom: 15rpx;
	font-size: 26rpx;
}

.detail-item .label {
	color: #999;
	width: 160rpx;
	flex-shrink: 0;
}

.detail-item .value {
	color: #333;
	flex: 1;
	word-break: break-all;
}

.detail-item .value.success {
	color: #52c41a;
}

.detail-item .value.failed {
	color: #ff4d4f;
}

.code-block {
	background: #f5f5f5;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 22rpx;
	color: #333;
	line-height: 1.5;
	white-space: pre-wrap;
	word-break: break-all;
	max-height: 400rpx;
	overflow-y: auto;
}

.popup-footer {
	padding: 30rpx;
	border-top: 1rpx solid #eee;
}

.btn-confirm {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #fff;
}
</style>
