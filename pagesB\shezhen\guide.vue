<template>
	<view class="guide-container">
		<!-- 顶部标题区域 -->
		<view class="header-section">
			<view class="header-icon-wrapper">
				<view class="header-icon">
					<text class="icon-text">🧠</text>
				</view>
				<view class="tech-lines">
					<view class="line line-1"></view>
					<view class="line line-2"></view>
					<view class="line line-3"></view>
				</view>
			</view>
			<text class="header-title">AI智能舌诊</text>
			<text class="header-subtitle">
				<text class="subtitle-icon">✨</text>
				通过舌象分析，了解身体健康状况
			</text>
		</view>

		<!-- 功能介绍区域 -->
		<view class="intro-section">
			<view class="intro-card">
				<view class="card-header">
					<view class="card-icon-wrapper">
						<text class="card-icon">🔬</text>
					</view>
					<text class="card-title">什么是舌诊？</text>
				</view>
				<text class="card-content">
					舌诊是中医诊断的重要方法之一，通过观察舌质、舌苔的形态、色泽、润燥等变化，来判断疾病的性质、病位的深浅、气血的盛衰、津液的存亡。
				</text>
			</view>

			<view class="intro-card">
				<view class="card-header">
					<view class="card-icon-wrapper ai-icon">
						<text class="card-icon">🤖</text>
					</view>
					<text class="card-title">AI舌诊功能</text>
				</view>
				<text class="card-content">
					运用先进的人工智能技术，结合传统中医理论，对舌象进行精准分析，为您提供个性化的健康建议和调理方案。
				</text>
			</view>
		</view>

		<!-- 使用指南区域 -->
		<view class="guide-section">
			<view class="section-header">
				<text class="section-icon">📋</text>
				<text class="section-title">拍摄指南</text>
			</view>
			<view class="guide-steps">
				<view class="step-item" v-for="(step, index) in guideSteps" :key="index">
					<view class="step-number">
						<text class="step-icon">{{ step.icon }}</text>
						<text class="step-num">{{ index + 1 }}</text>
					</view>
					<view class="step-content">
						<text class="step-title">{{ step.title }}</text>
						<text class="step-desc">{{ step.desc }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 注意事项区域 -->
		<view class="notice-section">
			<view class="section-header">
				<text class="section-icon">⚠️</text>
				<text class="section-title">注意事项</text>
			</view>
			<view class="notice-list">
				<view class="notice-item" v-for="(notice, index) in noticeList" :key="index">
					<view class="notice-icon-wrapper">
						<text class="notice-icon">{{ notice.icon }}</text>
					</view>
					<text class="notice-text">{{ notice.text }}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="action-section">
			<button class="start-btn" @click="startDiagnosis">
				<view class="btn-content">
					<text class="btn-icon">📸</text>
					<text class="btn-text">开始舌诊拍摄</text>
				</view>
				<view class="btn-glow"></view>
			</button>
			<text class="disclaimer">
				<text class="disclaimer-icon">ℹ️</text>
				本功能仅供健康参考，不能替代专业医生诊断
			</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ShezhenGuide',
	data() {
		return {
			// 拍摄指南步骤数据
			guideSteps: [
				{
					icon: '💡',
					title: '保持良好光线',
					desc: '在充足的自然光或白光下进行拍摄，避免暗光环境'
				},
				{
					icon: '🧽',
					title: '清洁口腔',
					desc: '拍摄前请清洁口腔，避免食物残留影响分析'
				},
				{
					icon: '👄',
					title: '正确姿势',
					desc: '张大嘴巴，充分露出舌头，保持舌面平整'
				},
				{
					icon: '📱',
					title: '稳定拍摄',
					desc: '保持手机稳定，将舌头置于画面中央进行拍摄'
				}
			],
			// 注意事项列表
			noticeList: [
				{
					icon: '⏰',
					text: '请在饭后30分钟后进行拍摄'
				},
				{
					icon: '🚫',
					text: '避免刚刷牙或使用漱口水后立即拍摄'
				},
				{
					icon: '🥤',
					text: '不要在饮用有色饮料后立即检测'
				},
				{
					icon: '👨‍⚕️',
					text: '如有舌部疾病请先咨询医生'
				},
				{
					icon: '👶',
					text: '孕妇、儿童使用前请咨询专业医生'
				}
			],
			// 舌诊配置信息
			configInfo: {
				isConfigured: false,
				usePublicAPI: true,
				remainingCalls: 0,
				callCost: 0
			}
		}
	},
	onLoad() {
		console.log('[2024-12-28 02:13:45] 舌诊指南页面加载完成');
		this.getShezhenConfig();
	},
	methods: {
		/**
		 * 获取舌诊配置信息
		 */
		getShezhenConfig() {
			var that = this;
			console.log('[2024-12-28 02:13:47] 开始获取舌诊配置');
			
			app.get('ApiSheZhen/getConfig', {}, function(res) {
				console.log('[2024-12-28 02:13:48] 获取配置成功：', res);
				if (res.status == 1) {
					that.configInfo = {
						isConfigured: res.data.isConfigured || false,
						usePublicAPI: res.data.usePublicAPI || false,
						remainingCalls: res.data.remainingCalls || 0,
						callCost: res.data.callCost || 0
					};
				} else {
					uni.showToast({
						title: res.msg || '获取配置失败',
						icon: 'none'
					});
				}
			});
		},
		
		/**
		 * 开始舌诊拍摄
		 * 跳转到拍摄页面
		 */
		startDiagnosis() {
			console.log('[2024-12-28 02:13:50] 开始舌诊 - 检查配置');
			
			// 检查是否有足够的分析次数
			if (this.configInfo.isConfigured && this.configInfo.remainingCalls <= 0) {
				uni.showToast({
					title: '分析次数不足，请联系管理员',
					icon: 'none'
				});
				return;
			}
			
			uni.navigateTo({
				url: '/pagesB/shezhen/camera',
				success: function() {
					console.log('[2024-12-28 02:13:52] 成功跳转到拍照页面');
				},
				fail: function() {
					console.log('[2024-12-28 02:13:53] 跳转到拍照页面失败');
					uni.showToast({
						title: '页面跳转失败',
						icon: 'error'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.guide-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
	padding: 20rpx;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
}

.guide-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background:
		radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
		radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
		radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);
	pointer-events: none;
}

/* 顶部标题区域样式 */
.header-section {
	text-align: center;
	padding: 60rpx 0 40rpx;
	color: white;
	position: relative;
	z-index: 1;
}

.header-icon-wrapper {
	position: relative;
	display: inline-block;
	margin-bottom: 30rpx;
}

.header-icon {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #00d4ff, #0099cc);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 0 30rpx rgba(0, 212, 255, 0.5);
	animation: pulse 2s infinite;
}

.icon-text {
	font-size: 60rpx;
}

.tech-lines {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 200rpx;
	height: 200rpx;
	pointer-events: none;
}

.line {
	position: absolute;
	background: linear-gradient(90deg, transparent, #00d4ff, transparent);
	opacity: 0.6;
	animation: rotate 3s linear infinite;
}

.line-1 {
	width: 100rpx;
	height: 2rpx;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotate(0deg);
}

.line-2 {
	width: 80rpx;
	height: 2rpx;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotate(120deg);
	animation-delay: -1s;
}

.line-3 {
	width: 60rpx;
	height: 2rpx;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotate(240deg);
	animation-delay: -2s;
}

.header-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
	background: linear-gradient(135deg, #00d4ff, #ffffff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.header-subtitle {
	display: block;
	font-size: 28rpx;
	opacity: 0.9;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
}

.subtitle-icon {
	animation: sparkle 1.5s ease-in-out infinite alternate;
}

/* 功能介绍区域样式 */
.intro-section {
	margin-bottom: 40rpx;
	position: relative;
	z-index: 1;
}

.intro-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.intro-card:hover {
	transform: translateY(-5rpx);
	box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.15);
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.card-icon-wrapper {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15rpx;
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.card-icon-wrapper.ai-icon {
	background: linear-gradient(135deg, #ff6b6b, #ee5a52);
	box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.3);
}

.card-icon {
	font-size: 30rpx;
	color: white;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.card-content {
	font-size: 28rpx;
	line-height: 1.6;
	color: #666;
}

/* 使用指南区域样式 */
.guide-section {
	margin-bottom: 40rpx;
	position: relative;
	z-index: 1;
}

.section-header {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
	gap: 10rpx;
}

.section-icon {
	font-size: 32rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.guide-steps {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.step-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 25rpx;
	transition: all 0.3s ease;
}

.step-item:last-child {
	margin-bottom: 0;
}

.step-item:hover {
	transform: translateX(10rpx);
}

.step-number {
	width: 70rpx;
	height: 70rpx;
	background: linear-gradient(135deg, #00d4ff, #0099cc);
	color: white;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	flex-shrink: 0;
	box-shadow: 0 4rpx 15rpx rgba(0, 212, 255, 0.3);
	position: relative;
	overflow: hidden;
}

.step-number::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	transform: rotate(45deg);
	animation: shine 2s infinite;
}

.step-icon {
	font-size: 20rpx;
	margin-bottom: 2rpx;
}

.step-num {
	font-size: 16rpx;
	font-weight: bold;
}

.step-content {
	flex: 1;
}

.step-title {
	display: block;
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.step-desc {
	display: block;
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

/* 注意事项区域样式 */
.notice-section {
	margin-bottom: 40rpx;
	position: relative;
	z-index: 1;
}

.notice-list {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.notice-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
	padding: 15rpx;
	border-radius: 10rpx;
	transition: all 0.3s ease;
}

.notice-item:last-child {
	margin-bottom: 0;
}

.notice-item:hover {
	background: rgba(255, 193, 7, 0.1);
	transform: translateX(5rpx);
}

.notice-icon-wrapper {
	width: 40rpx;
	height: 40rpx;
	background: linear-gradient(135deg, #ffc107, #ff9800);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15rpx;
	flex-shrink: 0;
	box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.3);
}

.notice-icon {
	font-size: 20rpx;
	color: white;
}

.notice-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	flex: 1;
}

/* 底部操作区域样式 */
.action-section {
	text-align: center;
	padding-bottom: 40rpx;
	position: relative;
	z-index: 1;
}

.start-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #ff6b6b, #ee5a52);
	border-radius: 50rpx;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.3);
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
}

.start-btn:active {
	transform: scale(0.98);
}

.start-btn::after {
	border: none;
}

.btn-content {
	display: flex;
	align-items: center;
	gap: 10rpx;
	position: relative;
	z-index: 2;
}

.btn-icon {
	font-size: 28rpx;
	animation: bounce 2s infinite;
}

.btn-text {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
}

.btn-glow {
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	transform: rotate(45deg);
	animation: glow 3s infinite;
}

.disclaimer {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	line-height: 1.4;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
}

.disclaimer-icon {
	font-size: 20rpx;
}

/* 动画效果 */
@keyframes pulse {
	0%, 100% {
		transform: scale(1);
		box-shadow: 0 0 30rpx rgba(0, 212, 255, 0.5);
	}
	50% {
		transform: scale(1.05);
		box-shadow: 0 0 40rpx rgba(0, 212, 255, 0.8);
	}
}

@keyframes rotate {
	from {
		transform: translate(-50%, -50%) rotate(0deg);
	}
	to {
		transform: translate(-50%, -50%) rotate(360deg);
	}
}

@keyframes sparkle {
	0% {
		opacity: 0.5;
		transform: scale(1);
	}
	100% {
		opacity: 1;
		transform: scale(1.2);
	}
}

@keyframes shine {
	0% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
	100% {
		transform: translateX(100%) translateY(100%) rotate(45deg);
	}
}

@keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		transform: translateY(0);
	}
	40% {
		transform: translateY(-10rpx);
	}
	60% {
		transform: translateY(-5rpx);
	}
}

@keyframes glow {
	0% {
		transform: translateX(-100%) translateY(-100%) rotate(45deg);
	}
	100% {
		transform: translateX(100%) translateY(100%) rotate(45deg);
	}
}
</style>