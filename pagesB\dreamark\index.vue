<template>
	<view class="container">
		<!-- 启动按钮覆盖层 -->
		<view v-if="showStartOverlay" class="start-overlay" @tap="startSystem">
			<view class="start-content">
				<view class="start-logo">
					<text class="logo-icon">🚀</text>
					<text class="logo-text">梦想方舟</text>
					<text class="logo-subtitle">DREAM ARK PROJECT</text>
				</view>
				<view class="start-btn" @tap.stop="startSystem"
					  :style="{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
					<text class="btn-icon">⚡</text>
					<text class="btn-text">启动系统</text>
				</view>
				<view class="start-tips">
					<text class="tip-text">准备开启时空对话之旅</text>
				</view>
			</view>
		</view>

		<!-- 穿越时空提示覆盖层 -->
		<view v-if="showTimePortalOverlay" class="time-portal-overlay">
			<view class="portal-content">
				<view class="portal-animation">
					<view class="portal-ring portal-ring-1"></view>
					<view class="portal-ring portal-ring-2"></view>
					<view class="portal-ring portal-ring-3"></view>
					<view class="portal-center">
						<text class="portal-icon">🌀</text>
					</view>
				</view>
				<text class="portal-text">正在穿越时空...</text>
				<text class="portal-subtitle">即将进入2049年</text>
			</view>
		</view>

		<!-- 粒子背景容器 -->
		<canvas canvas-id="particlesCanvas" class="particles-canvas"
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"></canvas>

		<!-- 装饰性背景 -->
		<view class="bg-grid"></view>
		<view class="bg-circles"></view>
		<view class="bg-stars"></view>
		
		<view class="console">
			<!-- 机器外壳装饰 -->
			<view class="machine-frame"></view>

			<!-- 状态指示灯 -->
			<view class="status-lights">
				<view class="status-light active"></view>
				<view class="status-light active"></view>
				<view class="status-light standby"></view>
			</view>

			<!-- 标题区域 -->
			<view class="title-section">
				<text class="main-title" :style="{color:t('color1')}">梦想方舟计划</text>
				<text class="subtitle">DREAM ARK PROJECT</text>
				<view class="title-line" :style="{background:t('color1')}"></view>
			</view>

			<!-- AI助手区域 -->
			<view class="ai-assistant">
				<view class="assistant-avatar">
					<view class="avatar-ring" :style="{borderColor:t('color1')}">
						<view class="avatar-core">
							<text class="avatar-icon">🤖</text>
						</view>
						<view class="avatar-pulse" :class="{speaking: isRobotSpeaking}"></view>
					</view>
					<text class="assistant-name" :style="{color:t('color1')}">明日萌像</text>
					<text class="assistant-role">时空舟长</text>
				</view>

				<!-- 对话输出区域 -->
				<view class="dialogue-output">
					<view class="output-header">
						<text class="output-label">系统消息</text>
						<view class="output-status" :class="{active: isRobotSpeaking}">
							<text class="status-dot"></text>
							<text class="status-text">{{isRobotSpeaking ? '正在输出' : '待机中'}}</text>
						</view>
					</view>
					<view class="output-content">
						<text class="output-text">{{displayText}}</text>
						<text v-if="showCursor" class="typing-cursor">|</text>
					</view>
				</view>

				<!-- 全息投影效果 -->
				<view class="hologram-display">
					<view class="hologram-grid">
						<view class="hologram-item" v-for="(icon, index) in hologramIcons" :key="index">
							<text class="hologram-icon">{{icon}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 系统状态面板 -->
			<view class="system-status">
				<view class="status-header">
					<text class="status-title">系统状态</text>
					<view class="status-indicator online">
						<text class="indicator-dot"></text>
						<text class="indicator-text">在线</text>
					</view>
				</view>
				<view class="status-grid">
					<view class="status-card">
						<text class="card-value" :style="{color:t('color1')}">98%</text>
						<text class="card-label">系统准备</text>
						<view class="card-progress">
							<view class="progress-bar" :style="{background:t('color1'), width: '98%'}"></view>
						</view>
					</view>
					<view class="status-card">
						<text class="card-value" :style="{color:t('color1')}">∞</text>
						<text class="card-label">时间能量</text>
						<view class="card-icon">⚡</view>
					</view>
					<view class="status-card">
						<text class="card-value" :style="{color:t('color1')}">2049</text>
						<text class="card-label">目标年代</text>
						<view class="card-icon">🎯</view>
					</view>
				</view>
			</view>

			<!-- 操作控制区域 -->
			<view class="control-panel">
				<!-- 清空数据选项 -->
				<view class="main-controls" v-if="hasDialogueData">
					<button class="secondary-btn" @tap="showClearDataDialog">
						<text class="btn-icon">🗑️</text>
						<text class="btn-text">清空已有数据</text>
					</button>
				</view>

				<!-- 辅助功能按钮 -->
				<view class="auxiliary-controls">
					<view class="control-item" @tap="toggleAudio">
						<text class="control-icon">{{audioEnabled ? '🔊' : '🔇'}}</text>
						<text class="control-label">音效</text>
					</view>
					<view class="control-item admin-control" @tap="adminClear">
						<text class="control-icon">⚙️</text>
						<text class="control-label">管理</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 浮动底部面板 -->
		<view v-if="showStartButton" class="floating-bottom-panel">
			<!-- 倒计时显示 -->
			<view v-if="autoJumpCountdown > 0" class="floating-countdown">
				<view class="countdown-ring">
					<view class="countdown-circle" :style="{background: 'conic-gradient(' + t('color1') + ' ' + (360 - (autoJumpCountdown / 3) * 360) + 'deg, rgba(0,247,255,0.2) 0deg)'}">
						<text class="countdown-number">{{autoJumpCountdown}}</text>
					</view>
				</view>
				<view class="countdown-info">
					<text class="countdown-title">自动穿越倒计时</text>
					<text class="countdown-subtitle">{{autoJumpCountdown}}秒后启动时空对话</text>
				</view>
				<button class="countdown-cancel" @tap="cancelAutoJump">
					<text class="cancel-icon">⏸</text>
				</button>
			</view>

			<!-- 启动按钮 -->
			<view class="floating-start-container">
				<button class="floating-start-btn" @tap="startDialogue"
						:style="{background:'linear-gradient(135deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 50%,'+t('color1')+' 100%)'}">
					<!-- 按钮装饰背景 -->
					<view class="btn-decoration">
						<view class="deco-grid"></view>
						<view class="deco-particles">
							<view class="deco-particle" v-for="i in 6" :key="i"></view>
						</view>
					</view>

					<!-- 按钮内容 -->
					<view class="btn-content">
						<view class="btn-icon-wrapper">
							<text class="btn-icon">🚀</text>
							<view class="icon-glow" :style="{boxShadow: '0 0 20rpx ' + t('color1')}"></view>
						</view>
						<view class="btn-text-wrapper">
							<text class="btn-text">启动时空对话</text>
							<text class="btn-subtitle">LAUNCH TIME DIALOGUE</text>
						</view>
						<view class="btn-arrow">
							<text class="arrow-icon">→</text>
						</view>
					</view>

					<!-- 扫描线效果 -->
					<view class="btn-scan-line" :style="{background:t('color1')}"></view>
				</button>
			</view>
		</view>

		<!-- 底部信息栏 -->
		<view class="footer-info">
			<view class="info-line">
				<text class="info-status" :style="{color:t('color1')}">● ARK SYSTEM ONLINE</text>
				<text class="info-divider">|</text>
				<text class="info-captain">舟长"明日萌像"已就位</text>
			</view>
			<view class="info-copyright">
				<text class="copyright-text">COPYRIGHT © 2049 DREAM ARK PROJECT</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			showStartOverlay: true,
			showTimePortalOverlay: false,
			displayText: '',
			showCursor: false,
			isRobotSpeaking: false,
			showStartButton: false,
			autoJumpCountdown: 0,
			autoJumpTimer: null,
			audioEnabled: true,
			hasDialogueData: false, // 是否有已保存的对话数据
			canvasWidth: 375,
			canvasHeight: 667,
			welcomeText: '欢迎来到梦想方舟计划！我是舟长"明日萌像"，将带您穿越时空，与2049年的自己对话。准备好开启这段奇妙的时空之旅了吗？',
			typingIndex: 0,
			typingTimer: null,
			particleSystem: null,
			hologramIcons: ['🚀', '⚛️', '🔬', '🌏', '⭐', '∞', '🎯', '💫'],
			pre_url: '', // 域名前缀
			openingAudio: null // 开场音频对象
		}
	},
	onLoad() {
		// 初始化云资源前缀URL
		const app = getApp();
		this.pre_url = app.globalData.pre_url || '';

		this.initCanvas();
		this.checkSystemState();
		this.checkDialogueData();
	},
	onReady() {
		this.initParticleSystem();

		// 延迟检查用户登录状态，确保页面完全加载
		setTimeout(() => {
			this.checkUserLogin();
		}, 500);
	},
	onUnload() {
		this.clearTypingTimer();
		this.clearAutoJumpTimer();
		this.destroyParticleSystem();
		this.destroyOpeningAudio();
	},
	methods: {
		// 检查用户登录状态
		checkUserLogin() {
			console.log('=== 检查用户登录状态 ===');
			const app = getApp();

			console.log('app.globalData:', app.globalData);
			console.log('app.globalData.mid:', app.globalData.mid);

			// 检查是否已登录 (mid为null、undefined或空字符串时才认为未登录，0是有效的用户ID)
			if (app.globalData.mid === null || app.globalData.mid === undefined || app.globalData.mid === '') {
				console.log('用户未登录，显示提示并跳转到个人中心');

				// 显示Toast提示
				uni.showToast({
					title: '请先登录后使用',
					icon: 'none',
					duration: 2000,
					success: () => {
						console.log('Toast显示成功');
					}
				});

				// 延迟跳转，让用户看到提示
				setTimeout(() => {
					console.log('开始跳转到个人中心');
					uni.reLaunch({
						url: '/pages/my/usercenter',
						success: () => {
							console.log('跳转到个人中心成功');
						},
						fail: (err) => {
							console.error('跳转到个人中心失败:', err);
							// 如果跳转失败，尝试跳转到登录页
							uni.reLaunch({
								url: '/pagesB/login/login',
								success: () => {
									console.log('跳转到登录页成功');
								},
								fail: (err2) => {
									console.error('跳转到登录页也失败:', err2);
								}
							});
						}
					});
				}, 1500);

				return false;
			}

			console.log('用户已登录，用户ID:', app.globalData.mid);
			return true;
		},

		// 初始化画布
		initCanvas() {
			const systemInfo = uni.getSystemInfoSync();
			this.canvasWidth = systemInfo.windowWidth;
			this.canvasHeight = systemInfo.windowHeight;
		},
		
		// 检查系统状态
		checkSystemState() {
			const savedData = uni.getStorageSync('user_dialogue_data');
			if (savedData) {
				this.showStartOverlay = false;
				this.startWelcomeSequence();
			}
		},
		
		// 启动系统
		startSystem() {
			// 检查登录状态
			if (!this.checkUserLogin()) {
				return;
			}

			this.showStartOverlay = false;
			this.playStartupSound();
			setTimeout(() => {
				this.startWelcomeSequence();
			}, 500);
		},
		
		// 开始欢迎序列
		startWelcomeSequence() {
			this.isRobotSpeaking = true;
			this.showCursor = true;
			this.playOpeningAudio(); // 播放开场音频
			this.startTypingEffect();
		},
		
		// 打字效果
		startTypingEffect() {
			this.clearTypingTimer();
			this.typingIndex = 0;
			this.displayText = '';
			
			this.typingTimer = setInterval(() => {
				if (this.typingIndex < this.welcomeText.length) {
					this.displayText += this.welcomeText[this.typingIndex];
					this.typingIndex++;
					this.playTypingSound();
				} else {
					this.completeTyping();
				}
			}, 80);
		},
		
		// 完成打字
		completeTyping() {
			this.clearTypingTimer();
			this.showCursor = false;
			this.isRobotSpeaking = false;

			setTimeout(() => {
				this.showStartButton = true;
				this.activateTimePortal();
				// 开始倒计时
				this.startAutoJumpCountdown();
			}, 1000);
		},
		
		// 清除打字定时器
		clearTypingTimer() {
			if (this.typingTimer) {
				clearInterval(this.typingTimer);
				this.typingTimer = null;
			}
		},
		
		// 激活时空通道
		activateTimePortal() {
			if (this.particleSystem) {
				this.particleSystem.activateTimePortal();
			}
		},
		
		// 启动对话（手动点击）
		startDialogue() {
			console.log('=== 启动时空对话被点击 ===');

			// 检查登录状态
			if (!this.checkUserLogin()) {
				return;
			}

			// 显示穿越时空动画
			this.showTimePortalOverlay = true;
			this.playPortalSound();
			this.activateTimePortal();

			// 2秒后跳转到对话页面
			setTimeout(() => {
				console.log('跳转到对话页面');
				this.showTimePortalOverlay = false;
				uni.navigateTo({
					url: '/pagesB/dreamark/dialogue'
				});
			}, 2000);
		},

		// 开始自动跳转倒计时
		startAutoJumpCountdown() {
			this.autoJumpCountdown = 3; // 3秒倒计时

			this.autoJumpTimer = setInterval(() => {
				this.autoJumpCountdown--;

				if (this.autoJumpCountdown <= 0) {
					this.clearAutoJumpTimer();
					this.autoStartDialogue();
				}
			}, 1000);
		},

		// 取消自动跳转
		cancelAutoJump() {
			this.clearAutoJumpTimer();
			this.autoJumpCountdown = 0;
			uni.showToast({
				title: '已取消自动穿越',
				icon: 'success',
				duration: 1500
			});
		},

		// 清除自动跳转定时器
		clearAutoJumpTimer() {
			if (this.autoJumpTimer) {
				clearInterval(this.autoJumpTimer);
				this.autoJumpTimer = null;
			}
		},

		// 自动启动对话（文字播放完成后）
		autoStartDialogue() {
			// 检查登录状态
			if (!this.checkUserLogin()) {
				return;
			}

			// 显示穿越时空动画
			this.showTimePortalOverlay = true;
			this.playPortalSound();
			this.activateTimePortal();

			// 2秒后跳转到对话页面
			setTimeout(() => {
				this.showTimePortalOverlay = false;
				uni.navigateTo({
					url: '/pagesB/dreamark/dialogue'
				});
			}, 2000);
		},
		
		// 切换音频
		toggleAudio() {
			this.audioEnabled = !this.audioEnabled;
			uni.showToast({
				title: this.audioEnabled ? '音效已开启' : '音效已关闭',
				icon: 'none'
			});
		},
		
		// 管理员清空
		adminClear() {
			uni.showModal({
				title: '管理员功能',
				content: '确定要清空所有用户数据吗？此操作不可恢复。',
				success: (res) => {
					if (res.confirm) {
						this.clearAllData();
					}
				}
			});
		},

		// 检查是否有对话数据
		checkDialogueData() {
			try {
				const savedData = uni.getStorageSync('user_dialogue_data');
				this.hasDialogueData = !!(savedData && Object.keys(savedData).length > 0);
				console.log('检查对话数据:', this.hasDialogueData, savedData);
			} catch (e) {
				console.error('检查对话数据失败:', e);
				this.hasDialogueData = false;
			}
		},

		// 显示清空数据确认对话框
		showClearDataDialog() {
			uni.showModal({
				title: '清空对话数据',
				content: '确定要清空已保存的对话数据吗？清空后将重新开始对话流程。',
				confirmText: '确定清空',
				cancelText: '取消',
				confirmColor: '#ff4444',
				success: (res) => {
					if (res.confirm) {
						this.clearDialogueData();
					}
				}
			});
		},

		// 清空对话数据
		clearDialogueData() {
			try {
				uni.removeStorageSync('user_dialogue_data');
				this.hasDialogueData = false;

				uni.showToast({
					title: '对话数据已清空',
					icon: 'success',
					duration: 2000
				});

				console.log('对话数据已清空');
			} catch (e) {
				console.error('清空对话数据失败:', e);
				uni.showToast({
					title: '清空失败',
					icon: 'error'
				});
			}
		},

		// 测试按钮点击
		testButtonClick() {
			console.log('=== 测试按钮被点击 ===');
			uni.showModal({
				title: '测试成功',
				content: '按钮点击事件正常工作！这说明基础的点击功能是正常的。',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '测试通过',
							icon: 'success'
						});
					}
				}
			});
		},

		// 清空所有数据
		clearAllData() {
			try {
				uni.clearStorageSync();
				uni.showToast({
					title: '数据已清空',
					icon: 'success'
				});
				setTimeout(() => {
					this.resetSystem();
				}, 1000);
			} catch (e) {
				uni.showToast({
					title: '清空失败',
					icon: 'error'
				});
			}
		},
		
		// 重置系统
		resetSystem() {
			this.showStartOverlay = true;
			this.displayText = '';
			this.showCursor = false;
			this.isRobotSpeaking = false;
			this.showStartButton = false;
			this.typingIndex = 0;
		},
		
		// 播放开场音频
		playOpeningAudio() {
			if (!this.audioEnabled) return;

			// 销毁之前的音频对象
			this.destroyOpeningAudio();

			// 创建新的音频对象
			this.openingAudio = uni.createInnerAudioContext();
			this.openingAudio.src = this.pre_url + '/static/MP3/kaichang.mp3';

			// 设置音频事件监听
			this.openingAudio.onError(() => {
				console.log('开场音频文件不存在，跳过播放');
				this.destroyOpeningAudio();
			});

			this.openingAudio.onEnded(() => {
				console.log('开场音频播放完成');
				this.destroyOpeningAudio();
			});

			// 尝试播放音频，如果文件不存在会触发onError
			try {
				this.openingAudio.play();
			} catch (error) {
				console.log('音频播放失败，跳过播放');
				this.destroyOpeningAudio();
			}
		},

		// 销毁开场音频
		destroyOpeningAudio() {
			if (this.openingAudio) {
				this.openingAudio.stop();
				this.openingAudio.destroy();
				this.openingAudio = null;
			}
		},

		// 播放启动音效
		playStartupSound() {
			if (!this.audioEnabled) return;

			const audio = uni.createInnerAudioContext();
			audio.src = this.pre_url + '/static/audio/startup.mp3';

			audio.onError(() => {
				console.log('启动音效文件不存在，跳过播放');
				audio.destroy();
			});

			try {
				audio.play();
			} catch (error) {
				console.log('启动音效播放失败，跳过播放');
				audio.destroy();
			}
		},

		// 播放打字音效
		playTypingSound() {
			if (!this.audioEnabled) return;
			// 简单的打字音效模拟
		},

		// 播放传送门音效
		playPortalSound() {
			if (!this.audioEnabled) return;

			const audio = uni.createInnerAudioContext();
			audio.src = this.pre_url + '/static/audio/portal.mp3';

			audio.onError(() => {
				console.log('传送门音效文件不存在，跳过播放');
				audio.destroy();
			});

			try {
				audio.play();
			} catch (error) {
				console.log('传送门音效播放失败，跳过播放');
				audio.destroy();
			}
		},
		
		// 初始化粒子系统
		initParticleSystem() {
			// 粒子系统初始化逻辑
			// 由于uniapp的canvas限制，这里简化实现
		},
		
		// 销毁粒子系统
		destroyParticleSystem() {
			if (this.particleSystem) {
				this.particleSystem = null;
			}
		}
	}
}
</script>

<style>
/* 基础样式 */
page {
	background: #0a0a2a;
	color: #ffffff;
	font-family: 'PingFang SC', 'Helvetica Neue', 'Arial', sans-serif;
}

/* 确保交互元素可点击 */
button, view {
	pointer-events: auto !important;
	-webkit-tap-highlight-color: transparent;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	user-select: none;
}

.container {
	position: relative;
	min-height: 100vh;
	background: linear-gradient(135deg, #0a0a2a 0%, #1a1a3a 30%, #2a2a4a 70%, #1a1a3a 100%);
	overflow: hidden;
	/* 科技感纹理效果 */
	background-image:
		radial-gradient(circle at 20% 80%, rgba(0, 247, 255, 0.05) 0%, transparent 50%),
		radial-gradient(circle at 80% 20%, rgba(189, 0, 255, 0.05) 0%, transparent 50%),
		radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.01) 0%, transparent 70%);
}

/* 启动覆盖层 */
.start-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, rgba(10, 10, 42, 0.95) 0%, rgba(26, 26, 58, 0.9) 100%);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	backdrop-filter: blur(15px);
}

.start-content {
	text-align: center;
	animation: startFadeIn 1.5s ease-out;
}

.start-logo {
	margin-bottom: 80rpx;
}

.logo-icon {
	display: block;
	font-size: 120rpx;
	margin-bottom: 30rpx;
	animation: logoFloat 3s ease-in-out infinite;
}

.logo-text {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
	color: transparent;
	margin-bottom: 15rpx;
}

.logo-subtitle {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
	letter-spacing: 6rpx;
	opacity: 0.8;
}

.start-tips {
	margin-top: 40rpx;
}

.tip-text {
	font-size: 28rpx;
	color: #a0a0ff;
	opacity: 0.7;
}

@keyframes startFadeIn {
	0% { opacity: 0; transform: translateY(50rpx); }
	100% { opacity: 1; transform: translateY(0); }
}

@keyframes logoFloat {
	0%, 100% { transform: translateY(0rpx); }
	50% { transform: translateY(-20rpx); }
}

.start-btn {
	background: linear-gradient(45deg, #00f7ff, #bd00ff) !important;
	color: #fff !important;
	padding: 40rpx 80rpx !important;
	border-radius: 60rpx !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	gap: 25rpx !important;
	box-shadow:
		0 20rpx 50rpx rgba(0, 247, 255, 0.3),
		0 10rpx 25rpx rgba(189, 0, 255, 0.2),
		inset 0 2rpx 0 rgba(255, 255, 255, 0.2) !important;
	animation: startPulse 2.5s ease-in-out infinite;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
	border: none !important;
	width: auto !important;
	margin-bottom: 20rpx;
	font-size: 32rpx !important;
	font-weight: bold !important;
}

.start-btn::after {
	border: none !important;
}

.start-btn::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.15), transparent);
	transform: rotate(45deg);
	animation: shimmer 4s infinite;
}

.start-btn:active {
	transform: scale(0.95);
	box-shadow:
		0 10rpx 30rpx rgba(0, 247, 255, 0.4),
		0 5rpx 15rpx rgba(189, 0, 255, 0.3);
}

.btn-icon {
	font-size: 36rpx;
}

.btn-text {
	font-size: 32rpx;
	font-weight: bold;
}

/* 清空数据选项 */
.clear-data-option {
	background: rgba(255, 68, 68, 0.1) !important;
	border: 1px solid rgba(255, 68, 68, 0.3) !important;
	color: #ff6666 !important;
	padding: 20rpx 40rpx !important;
	border-radius: 40rpx !important;
	display: flex !important;
	align-items: center !important;
	gap: 15rpx !important;
	margin-top: 20rpx;
	transition: all 0.3s ease;
	width: auto !important;
}

.clear-data-option::after {
	border: none !important;
}

.clear-data-option:active {
	transform: scale(0.95);
	background: rgba(255, 68, 68, 0.2) !important;
	border-color: #ff4444 !important;
}

.clear-icon {
	font-size: 20rpx;
}

.clear-text {
	font-size: 24rpx;
}

/* 测试按钮 */
.test-button {
	background: rgba(0, 255, 0, 0.1) !important;
	border: 1px solid rgba(0, 255, 0, 0.3) !important;
	color: #00ff00 !important;
	padding: 20rpx 40rpx !important;
	border-radius: 40rpx !important;
	margin-top: 20rpx;
	font-size: 24rpx !important;
	width: auto !important;
}

.test-button::after {
	border: none !important;
}

.start-btn .icon {
	font-size: 48rpx;
}

.start-btn .btn-text {
	font-size: 32rpx;
	font-weight: bold;
}

@keyframes startPulse {
	0% { transform: scale(0.95); box-shadow: 0 15rpx 40rpx rgba(0, 247, 255, 0.4); }
	50% { transform: scale(1.05); box-shadow: 0 20rpx 50rpx rgba(0, 247, 255, 0.6); }
	100% { transform: scale(0.95); box-shadow: 0 15rpx 40rpx rgba(0, 247, 255, 0.4); }
}

@keyframes shimmer {
	0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
	100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 穿越时空覆盖层 */
.time-portal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(circle, rgba(0, 247, 255, 0.1), rgba(10, 10, 42, 0.95));
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 2000;
	backdrop-filter: blur(10px);
}

.portal-content {
	text-align: center;
}

.portal-animation {
	position: relative;
	width: 300rpx;
	height: 300rpx;
	margin: 0 auto 60rpx;
}

.portal-ring {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border: 3px solid rgba(0, 247, 255, 0.6);
	border-radius: 50%;
	animation: portalExpand 2s infinite;
}

.portal-ring-1 {
	width: 100rpx;
	height: 100rpx;
	animation-delay: 0s;
}

.portal-ring-2 {
	width: 150rpx;
	height: 150rpx;
	animation-delay: 0.5s;
}

.portal-ring-3 {
	width: 200rpx;
	height: 200rpx;
	animation-delay: 1s;
}

@keyframes portalExpand {
	0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.8; }
	50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.4; }
	100% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

.portal-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 80rpx;
	height: 80rpx;
	background: radial-gradient(circle, rgba(0, 247, 255, 0.8), transparent);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: portalSpin 2s linear infinite;
}

@keyframes portalSpin {
	0% { transform: translate(-50%, -50%) rotate(0deg); }
	100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.portal-icon {
	font-size: 48rpx;
	color: #00f7ff;
}

.portal-text {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 20rpx;
	animation: portalTextGlow 1.5s infinite alternate;
}

@keyframes portalTextGlow {
	0% { text-shadow: 0 0 20rpx #00f7ff; }
	100% { text-shadow: 0 0 40rpx #00f7ff, 0 0 60rpx #00f7ff; }
}

.portal-subtitle {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
}

/* 粒子画布 */
.particles-canvas {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1;
	pointer-events: none;
}

/* 装饰背景 */
.bg-grid {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		linear-gradient(rgba(0, 247, 255, 0.08) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 247, 255, 0.08) 1px, transparent 1px);
	background-size: 60px 60px;
	z-index: 2;
	animation: gridMove 25s linear infinite;
	opacity: 0.6;
}

.bg-circles {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 2;
}

.bg-circles::before {
	content: '';
	position: absolute;
	top: 15%;
	left: 8%;
	width: 250px;
	height: 250px;
	border: 2px solid rgba(189, 0, 255, 0.2);
	border-radius: 50%;
	animation: float 8s ease-in-out infinite;
}

.bg-circles::after {
	content: '';
	position: absolute;
	bottom: 15%;
	right: 8%;
	width: 180px;
	height: 180px;
	border: 2px solid rgba(255, 0, 200, 0.2);
	border-radius: 50%;
	animation: float 10s ease-in-out infinite reverse;
}

.bg-stars {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
	background-image:
		radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
		radial-gradient(2px 2px at 40px 70px, rgba(0, 247, 255, 0.4), transparent),
		radial-gradient(1px 1px at 90px 40px, rgba(189, 0, 255, 0.3), transparent),
		radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.2), transparent);
	background-repeat: repeat;
	background-size: 200px 100px;
	animation: starsMove 30s linear infinite;
}

@keyframes gridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(60px, 60px); }
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-30px) rotate(180deg); }
}

@keyframes starsMove {
	0% { transform: translateX(0); }
	100% { transform: translateX(-200px); }
}

/* 主控制台 */
.console {
	position: relative;
	z-index: 10;
	max-width: 750rpx;
	margin: 80rpx auto 0;
	padding: 50rpx 40rpx;
	background: rgba(0, 0, 0, 0.4);
	border: 2px solid rgba(0, 247, 255, 0.3);
	border-radius: 25rpx;
	backdrop-filter: blur(20px);
	box-shadow:
		0 20rpx 60rpx rgba(0, 0, 0, 0.3),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

/* 机器外壳装饰 */
.machine-frame {
	position: absolute;
	top: -5rpx;
	left: -5rpx;
	right: -5rpx;
	bottom: -5rpx;
	border: 1px solid rgba(189, 0, 255, 0.2);
	border-radius: 30rpx;
	background: linear-gradient(45deg, transparent, rgba(0, 247, 255, 0.05), transparent);
	animation: frameGlow 4s ease-in-out infinite alternate;
}

@keyframes frameGlow {
	0% { opacity: 0.5; }
	100% { opacity: 1; }
}

/* 状态指示灯 */
.status-lights {
	display: flex;
	gap: 15rpx;
	margin-bottom: 40rpx;
	justify-content: center;
}

.status-light {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	transition: all 0.3s ease;
}

.status-light.active {
	background: #00ff80;
	box-shadow: 0 0 15rpx #00ff80;
	animation: activeBlink 2s infinite;
}

.status-light.standby {
	background: #666;
	box-shadow: 0 0 5rpx #666;
	animation: standbyBlink 3s infinite;
}

@keyframes activeBlink {
	0%, 80% { opacity: 1; }
	90%, 100% { opacity: 0.3; }
}

@keyframes standbyBlink {
	0%, 90% { opacity: 0.3; }
	95%, 100% { opacity: 1; }
}

/* 标题区域 */
.title-section {
	text-align: center;
	margin-bottom: 60rpx;
}

.main-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
	color: transparent;
	margin-bottom: 15rpx;
	animation: titleGlow 3s ease-in-out infinite alternate;
}

.subtitle {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
	letter-spacing: 6rpx;
	opacity: 0.8;
	margin-bottom: 20rpx;
}

.title-line {
	width: 100rpx;
	height: 4rpx;
	margin: 0 auto;
	border-radius: 2rpx;
	animation: lineExpand 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
	0% { filter: brightness(1); }
	100% { filter: brightness(1.2); }
}

@keyframes lineExpand {
	0% { width: 100rpx; }
	100% { width: 150rpx; }
}

/* AI助手区域 */
.ai-assistant {
	margin-bottom: 60rpx;
}

.assistant-avatar {
	text-align: center;
	margin-bottom: 40rpx;
}

.avatar-ring {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	margin: 0 auto 20rpx;
	border: 3rpx solid;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: avatarRotate 10s linear infinite;
}

.avatar-core {
	width: 80rpx;
	height: 80rpx;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
}

.avatar-icon {
	font-size: 40rpx;
}

.avatar-pulse {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	right: -10rpx;
	bottom: -10rpx;
	border: 2rpx solid rgba(0, 247, 255, 0.3);
	border-radius: 50%;
	opacity: 0;
}

.avatar-pulse.speaking {
	animation: avatarPulse 1.5s ease-in-out infinite;
}

.assistant-name {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.assistant-role {
	display: block;
	font-size: 24rpx;
	color: #a0a0ff;
	opacity: 0.7;
}

@keyframes avatarRotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes avatarPulse {
	0% { opacity: 0; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.1); }
	100% { opacity: 0; transform: scale(1.2); }
}

/* 对话输出区域 */
.dialogue-output {
	margin-bottom: 40rpx;
}

.output-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 0 20rpx;
}

.output-label {
	font-size: 24rpx;
	color: #7df9ff;
	opacity: 0.8;
}

.output-status {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #666;
	transition: all 0.3s ease;
}

.output-status.active .status-dot {
	background: #00ff80;
	box-shadow: 0 0 10rpx #00ff80;
	animation: statusPulse 1s infinite;
}

.status-text {
	font-size: 20rpx;
	color: #999;
}

.output-status.active .status-text {
	color: #00ff80;
}

.output-content {
	background: rgba(0, 0, 0, 0.6);
	border: 1px solid rgba(0, 247, 255, 0.2);
	border-radius: 15rpx;
	padding: 40rpx;
	min-height: 200rpx;
	position: relative;
	backdrop-filter: blur(10rpx);
}

.output-text {
	color: #e0e0ff;
	font-size: 28rpx;
	line-height: 1.6;
	letter-spacing: 1rpx;
}

.typing-cursor {
	color: #00f7ff;
	font-size: 28rpx;
	animation: cursorBlink 1s infinite;
}

@keyframes statusPulse {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.5; }
}

@keyframes cursorBlink {
	0%, 50% { opacity: 1; }
	51%, 100% { opacity: 0; }
}

/* 全息投影显示 */
.hologram-display {
	margin-bottom: 40rpx;
}

.hologram-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 20rpx;
	animation: hologramFloat 4s ease-in-out infinite;
}

.hologram-item {
	width: 80rpx;
	height: 80rpx;
	background: rgba(0, 247, 255, 0.08);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
}

.hologram-item::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent, rgba(0, 247, 255, 0.1), transparent);
	animation: hologramScan 3s linear infinite;
}

.hologram-item:nth-child(odd) {
	animation: hologramRotate1 8s linear infinite;
}

.hologram-item:nth-child(even) {
	animation: hologramRotate2 6s linear infinite reverse;
}

.hologram-icon {
	font-size: 36rpx;
	z-index: 1;
	position: relative;
}

@keyframes hologramFloat {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-15px); }
}

@keyframes hologramScan {
	0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
	100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes hologramRotate1 {
	0% { transform: rotateY(0deg) rotateZ(0deg); }
	100% { transform: rotateY(360deg) rotateZ(180deg); }
}

@keyframes hologramRotate2 {
	0% { transform: rotateX(0deg) rotateZ(0deg); }
	100% { transform: rotateX(360deg) rotateZ(-180deg); }
}

/* 系统状态面板 */
.system-status {
	margin-bottom: 60rpx;
}

.status-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding: 0 20rpx;
}

.status-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #7df9ff;
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.indicator-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #00ff80;
	box-shadow: 0 0 10rpx #00ff80;
	animation: indicatorPulse 2s infinite;
}

.indicator-text {
	font-size: 22rpx;
	color: #00ff80;
}

.status-grid {
	display: flex;
	gap: 20rpx;
	justify-content: space-between;
}

.status-card {
	flex: 1;
	background: rgba(0, 0, 0, 0.4);
	border: 1px solid rgba(0, 247, 255, 0.2);
	border-radius: 15rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	position: relative;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.status-card:hover {
	border-color: rgba(0, 247, 255, 0.4);
	transform: translateY(-5rpx);
}

.card-value {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.card-label {
	display: block;
	font-size: 22rpx;
	color: #a0a0ff;
	margin-bottom: 15rpx;
}

.card-progress {
	width: 100%;
	height: 6rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 3rpx;
	overflow: hidden;
}

.progress-bar {
	height: 100%;
	border-radius: 3rpx;
	transition: width 0.5s ease;
	animation: progressGlow 2s ease-in-out infinite alternate;
}

.card-icon {
	position: absolute;
	top: 15rpx;
	right: 15rpx;
	font-size: 24rpx;
	opacity: 0.3;
}

@keyframes indicatorPulse {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.5; }
}

@keyframes progressGlow {
	0% { box-shadow: 0 0 5rpx rgba(0, 247, 255, 0.3); }
	100% { box-shadow: 0 0 15rpx rgba(0, 247, 255, 0.6); }
}

/* 操作控制区域 */
.control-panel {
	margin-bottom: 40rpx;
}

.main-controls {
	margin-bottom: 30rpx;
}

.primary-btn {
	width: 100%;
	background: linear-gradient(45deg, #00f7ff, #bd00ff) !important;
	color: #fff !important;
	padding: 35rpx 0 !important;
	border-radius: 50rpx !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	gap: 20rpx !important;
	box-shadow: 0 15rpx 40rpx rgba(0, 247, 255, 0.3) !important;
	transition: all 0.3s ease;
	border: none !important;
	font-size: 32rpx !important;
	font-weight: bold !important;
	position: relative;
	overflow: hidden;
}

.primary-btn::after {
	border: none !important;
}

.primary-btn::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
	animation: buttonShimmer 3s infinite;
}

.primary-btn:active {
	transform: scale(0.98);
	box-shadow: 0 10rpx 25rpx rgba(0, 247, 255, 0.4) !important;
}

.secondary-btn {
	width: 100%;
	background: rgba(255, 68, 68, 0.1) !important;
	border: 1px solid rgba(255, 68, 68, 0.3) !important;
	color: #ff6666 !important;
	padding: 25rpx 0 !important;
	border-radius: 40rpx !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	gap: 15rpx !important;
	margin-top: 20rpx;
	transition: all 0.3s ease;
	font-size: 28rpx !important;
}

.secondary-btn::after {
	border: none !important;
}

.secondary-btn:active {
	transform: scale(0.95);
	background: rgba(255, 68, 68, 0.2) !important;
}

@keyframes buttonShimmer {
	0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
	100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 倒计时面板 */
.countdown-panel {
	background: rgba(255, 165, 0, 0.08);
	border: 1px solid rgba(255, 165, 0, 0.3);
	border-radius: 20rpx;
	padding: 30rpx;
	margin: 30rpx 0;
	animation: countdownPulse 2s infinite;
}

.countdown-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.countdown-text {
	font-size: 28rpx;
	color: #ffa500;
	font-weight: bold;
}

.countdown-progress {
	position: relative;
}

.progress-ring {
	width: 60rpx;
	height: 60rpx;
	border: 3rpx solid rgba(255, 165, 0, 0.3);
	border-top: 3rpx solid #ffa500;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: progressSpin 1s linear infinite;
}

.progress-number {
	font-size: 24rpx;
	color: #ffa500;
	font-weight: bold;
}

.cancel-btn {
	width: 100%;
	background: rgba(255, 68, 68, 0.1) !important;
	border: 1px solid #ff4444 !important;
	color: #ff4444 !important;
	padding: 20rpx 0 !important;
	border-radius: 30rpx !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	gap: 10rpx !important;
	transition: all 0.3s ease;
	font-size: 24rpx !important;
}

.cancel-btn::after {
	border: none !important;
}

.cancel-btn:active {
	transform: scale(0.95);
	background: rgba(255, 68, 68, 0.2) !important;
}

/* 辅助功能控制 */
.auxiliary-controls {
	display: flex;
	gap: 30rpx;
	justify-content: center;
	margin-top: 30rpx;
}

.control-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	padding: 20rpx;
	background: rgba(0, 247, 255, 0.08);
	border: 1px solid rgba(0, 247, 255, 0.2);
	border-radius: 15rpx;
	transition: all 0.3s ease;
	min-width: 100rpx;
}

.control-item:active {
	transform: scale(0.95);
	background: rgba(0, 247, 255, 0.15);
	border-color: rgba(0, 247, 255, 0.4);
}

.admin-control {
	background: rgba(255, 68, 68, 0.08);
	border-color: rgba(255, 68, 68, 0.2);
}

.admin-control:active {
	background: rgba(255, 68, 68, 0.15);
	border-color: rgba(255, 68, 68, 0.4);
}

.control-icon {
	font-size: 32rpx;
}

.control-label {
	font-size: 22rpx;
	color: #a0a0ff;
}

.admin-control .control-label {
	color: #ff6666;
}

@keyframes countdownPulse {
	0%, 100% { opacity: 0.8; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.02); }
}

@keyframes progressSpin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 浮动底部面板 */
.floating-bottom-panel {
	position: fixed;
	bottom: 120rpx;
	left: 30rpx;
	right: 30rpx;
	z-index: 200;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

/* 浮动倒计时 */
.floating-countdown {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx 30rpx;
	background: rgba(0, 0, 0, 0.8);
	border: 2rpx solid rgba(255, 165, 0, 0.5);
	border-radius: 25rpx;
	backdrop-filter: blur(15rpx);
	animation: countdownFloat 2s ease-in-out infinite;
}

@keyframes countdownFloat {
	0%, 100% {
		transform: translateY(0);
		box-shadow: 0 10rpx 30rpx rgba(255, 165, 0, 0.2);
	}
	50% {
		transform: translateY(-5rpx);
		box-shadow: 0 15rpx 40rpx rgba(255, 165, 0, 0.3);
	}
}

.countdown-ring {
	position: relative;
}

.countdown-circle {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.countdown-circle::before {
	content: '';
	position: absolute;
	top: -3rpx;
	left: -3rpx;
	right: -3rpx;
	bottom: -3rpx;
	border-radius: 50%;
	background: rgba(255, 165, 0, 0.2);
	animation: countdownRingPulse 1s ease-in-out infinite;
}

@keyframes countdownRingPulse {
	0%, 100% {
		opacity: 0.3;
		transform: scale(0.9);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.1);
	}
}

.countdown-number {
	font-size: 28rpx;
	font-weight: bold;
	color: #ffa500;
	z-index: 2;
	text-shadow: 0 0 10rpx #ffa500;
}

.countdown-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 5rpx;
}

.countdown-title {
	font-size: 24rpx;
	color: #ffa500;
	font-weight: bold;
}

.countdown-subtitle {
	font-size: 20rpx;
	color: rgba(255, 165, 0, 0.7);
}

.countdown-cancel {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background: rgba(255, 68, 68, 0.2);
	border: 2rpx solid rgba(255, 68, 68, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.countdown-cancel:active {
	transform: scale(0.9);
	background: rgba(255, 68, 68, 0.3);
}

.cancel-icon {
	font-size: 24rpx;
	color: #ff6666;
}

/* 浮动启动按钮容器 */
.floating-start-container {
	position: relative;
}

.floating-start-btn {
	position: relative;
	width: 100%;
	padding: 0;
	border: none;
	border-radius: 30rpx;
	overflow: hidden;
	box-shadow:
		0 20rpx 60rpx rgba(0, 247, 255, 0.3),
		0 0 0 2rpx rgba(0, 247, 255, 0.2);
	transition: all 0.3s ease;
}

.floating-start-btn:active {
	transform: scale(0.98);
	box-shadow:
		0 15rpx 45rpx rgba(0, 247, 255, 0.4),
		0 0 0 2rpx rgba(0, 247, 255, 0.4);
}

/* 按钮装饰背景 */
.btn-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
}

.deco-grid {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image:
		linear-gradient(rgba(255, 255, 255, 0.05) 1rpx, transparent 1rpx),
		linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1rpx, transparent 1rpx);
	background-size: 20rpx 20rpx;
	animation: decoGridMove 8s linear infinite;
}

@keyframes decoGridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(20rpx, 20rpx); }
}

.deco-particles {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.deco-particle {
	position: absolute;
	width: 3rpx;
	height: 3rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 50%;
	animation: decoParticleFloat 3s ease-in-out infinite;
}

.deco-particle:nth-child(1) { top: 20%; left: 15%; animation-delay: 0s; }
.deco-particle:nth-child(2) { top: 30%; left: 75%; animation-delay: 0.5s; }
.deco-particle:nth-child(3) { top: 60%; left: 25%; animation-delay: 1s; }
.deco-particle:nth-child(4) { top: 70%; left: 80%; animation-delay: 1.5s; }
.deco-particle:nth-child(5) { top: 40%; left: 50%; animation-delay: 2s; }
.deco-particle:nth-child(6) { top: 80%; left: 60%; animation-delay: 2.5s; }

@keyframes decoParticleFloat {
	0%, 100% {
		opacity: 0.3;
		transform: translateY(0) scale(0.5);
	}
	50% {
		opacity: 1;
		transform: translateY(-10rpx) scale(1);
	}
}

/* 按钮内容 */
.btn-content {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	z-index: 2;
}

.btn-icon-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn-icon {
	font-size: 32rpx;
	z-index: 2;
}

.icon-glow {
	position: absolute;
	top: -8rpx;
	left: -8rpx;
	right: -8rpx;
	bottom: -8rpx;
	border-radius: 50%;
	animation: iconGlowPulse 2s ease-in-out infinite;
}

@keyframes iconGlowPulse {
	0%, 100% {
		opacity: 0.3;
		transform: scale(0.8);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.2);
	}
}

.btn-text-wrapper {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 3rpx;
	margin: 0 20rpx;
}

.btn-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #ffffff;
	text-shadow: 0 0 15rpx rgba(255, 255, 255, 0.5);
}

.btn-subtitle {
	font-size: 18rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 300;
	letter-spacing: 2rpx;
}

.btn-arrow {
	display: flex;
	align-items: center;
	justify-content: center;
}

.arrow-icon {
	font-size: 28rpx;
	color: #ffffff;
	animation: arrowMove 2s ease-in-out infinite;
}

@keyframes arrowMove {
	0%, 100% { transform: translateX(0); }
	50% { transform: translateX(5rpx); }
}

/* 扫描线效果 */
.btn-scan-line {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	opacity: 0.6;
	animation: btnScanMove 3s linear infinite;
}

@keyframes btnScanMove {
	0% { left: -100%; opacity: 0; }
	50% { opacity: 0.6; }
	100% { left: 100%; opacity: 0; }
}

/* 底部信息栏 */
.footer-info {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 25rpx 30rpx;
	background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.9) 100%);
	border-top: 1px solid rgba(0, 247, 255, 0.2);
	z-index: 100;
	backdrop-filter: blur(15rpx);
}

.info-line {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	margin-bottom: 8rpx;
}

.info-status {
	font-size: 22rpx;
	font-weight: bold;
	animation: statusBlink 2s infinite;
}

.info-divider {
	font-size: 20rpx;
	color: #666;
}

.info-captain {
	font-size: 22rpx;
	color: #a0a0ff;
}

.info-copyright {
	text-align: center;
}

.copyright-text {
	font-size: 18rpx;
	color: #666;
	opacity: 0.7;
	letter-spacing: 1rpx;
}

@keyframes statusBlink {
	0%, 90% { opacity: 1; }
	95%, 100% { opacity: 0.6; }
}
</style>
