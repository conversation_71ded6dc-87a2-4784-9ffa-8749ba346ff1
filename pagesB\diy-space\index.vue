<template>
<view class="container">
	<block v-if="isload">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @tap="goBack">
					<text class="back-icon">‹</text>
				</view>
				<view class="navbar-title">DIY内心空间</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 头部介绍区域 -->
			<view class="intro-section">
				<view class="intro-icon">
					<view class="icon-bg">
						<text class="test-icon">🏠</text>
					</view>
				</view>
				<view class="intro-title">DIY内心空间</view>
				<view class="intro-desc">打造专属的内心世界，探索自我内在的无限可能</view>
			</view>

			<!-- 特色功能介绍 -->
			<view class="features-section">
				<view class="feature-item">
					<view class="feature-icon">
						<text class="feature-emoji">🎨</text>
					</view>
					<view class="feature-text">
						<view class="feature-title">个性定制</view>
						<view class="feature-desc">自由设计属于你的内心空间布局</view>
					</view>
				</view>
				<view class="feature-item">
					<view class="feature-icon">
						<text class="feature-emoji">🌟</text>
					</view>
					<view class="feature-text">
						<view class="feature-title">心灵探索</view>
						<view class="feature-desc">深入了解内心深处的真实想法</view>
					</view>
				</view>
				<view class="feature-item">
					<view class="feature-icon">
						<text class="feature-emoji">💎</text>
					</view>
					<view class="feature-text">
						<view class="feature-title">创意无限</view>
						<view class="feature-desc">释放想象力，创造独特的心灵空间</view>
					</view>
				</view>
			</view>

			<!-- 开始测试按钮 -->
			<view class="start-section">
				<view class="start-btn" @tap="jumpToDIYSpace">
					<text class="start-text">开始测试</text>
					<text class="start-arrow">→</text>
				</view>
				<view class="start-tip">探索时间随心而定</view>
			</view>
		</view>

		<!-- 加载组件 -->
		<loading v-if="loading"></loading>
	</block>
</view>
</template>

<script>
import loading from '@/components/loading/loading.vue';

export default {
	components: {
		loading
	},
	
	data() {
		return {
			isload: false,
			loading: false
		};
	},

	onLoad: function() {
		this.loaded();
	},

	methods: {
		// 页面加载完成
		loaded: function() {
			this.isload = true;
		},

		// 跳转到DIY内心空间小程序
		jumpToDIYSpace: function() {
			uni.showModal({
				title: '跳转确认',
				content: '即将跳转到DIY内心空间小程序，是否继续？',
				confirmText: '确定',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.confirmJump();
					}
				}
			});
		},

		// 确认跳转
		confirmJump: function() {
			// 显示加载提示
			uni.showLoading({
				title: '正在跳转...'
			});

			// 检查平台支持
			// #ifdef MP-WEIXIN
			// 跳转到DIY内心空间小程序
			wx.navigateToMiniProgram({
				appId: 'wx25eae5e5df8a26ca',
				path: 'pages/index/index',
				extraData: {
					q: 'https%3A%2F%2Fput-qrcode.sol-dawning.com%2Fcustomer.html%3Fid%3D354%26type%3D1'
				},
				envVersion: 'release',
				success: function(res) {
					uni.hideLoading();
					console.log('跳转成功', res);
				},
				fail: function(err) {
					uni.hideLoading();
					console.error('跳转失败', err);
					uni.showToast({
						title: '跳转失败，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			});
			// #endif

			// #ifndef MP-WEIXIN
			uni.hideLoading();
			uni.showToast({
				title: '此功能仅在微信小程序中可用',
				icon: 'none',
				duration: 2000
			});
			// #endif
		},

		// 返回上一页
		goBack: function() {
			uni.navigateBack();
		}
	}
};
</script>

<style>
.container {
	background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
	min-height: 100vh;
	padding: 0;
	margin: 0;
	position: relative;
	overflow: hidden;
}

.container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: 
		radial-gradient(circle at 20% 20%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
		radial-gradient(circle at 80% 80%, rgba(108, 92, 231, 0.1) 0%, transparent 50%),
		radial-gradient(circle at 40% 60%, rgba(0, 212, 170, 0.05) 0%, transparent 50%);
	pointer-events: none;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(15, 15, 35, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 122, 255, 0.2);
	box-shadow: 0 2rpx 20rpx rgba(0, 122, 255, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 30rpx;
	padding-top: var(--status-bar-height);
}

.navbar-left {
	width: 80rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 30rpx;
	background: rgba(0, 122, 255, 0.1);
	border: 1rpx solid rgba(0, 122, 255, 0.3);
}

.navbar-left .back-icon {
	font-size: 48rpx;
	color: #007AFF;
	font-weight: bold;
	line-height: 1;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 0 10rpx rgba(0, 122, 255, 0.5);
}

.navbar-right {
	width: 80rpx;
}

/* 主要内容区域 */
.main-content {
	padding-top: calc(88rpx + var(--status-bar-height) + 40rpx);
	padding-bottom: 40rpx;
	padding-left: 30rpx;
	padding-right: 30rpx;
}

/* 头部介绍区域 */
.intro-section {
	text-align: center;
	margin-bottom: 60rpx;
}

.intro-icon {
	margin-bottom: 30rpx;
}

.icon-bg {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	background: linear-gradient(135deg, #007AFF 0%, #6C5CE7 100%);
	box-shadow: 
		0 20rpx 40rpx rgba(0, 122, 255, 0.3),
		0 0 60rpx rgba(0, 122, 255, 0.2),
		inset 0 0 20rpx rgba(255, 255, 255, 0.1);
	animation: pulse 2s ease-in-out infinite;
}

.icon-bg .test-icon {
	font-size: 60rpx;
	color: #fff;
	text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
	line-height: 1;
}

.intro-title {
	font-size: 48rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 20rpx;
	text-shadow: 0 0 20rpx rgba(0, 122, 255, 0.5);
}

.intro-desc {
	font-size: 28rpx;
	color: #b8c5d6;
	line-height: 1.6;
	max-width: 500rpx;
	margin: 0 auto;
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
		box-shadow:
			0 20rpx 40rpx rgba(0, 122, 255, 0.3),
			0 0 60rpx rgba(0, 122, 255, 0.2);
	}
	50% {
		transform: scale(1.05);
		box-shadow:
			0 25rpx 50rpx rgba(0, 122, 255, 0.4),
			0 0 80rpx rgba(0, 122, 255, 0.3);
	}
}

/* 特色功能区域 */
.features-section {
	margin-bottom: 60rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.05);
	border: 1rpx solid rgba(0, 122, 255, 0.2);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow:
		0 8rpx 30rpx rgba(0, 0, 0, 0.3),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
}

.feature-item:active {
	transform: translateY(-2rpx);
	box-shadow:
		0 12rpx 40rpx rgba(0, 122, 255, 0.2),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
	border-color: rgba(0, 122, 255, 0.4);
}

.feature-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	background: linear-gradient(135deg, rgba(0, 122, 255, 0.2), rgba(108, 92, 231, 0.2));
	border: 1rpx solid rgba(0, 122, 255, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
	box-shadow: 0 0 20rpx rgba(0, 122, 255, 0.1);
}

.feature-icon .feature-emoji {
	font-size: 36rpx;
	color: #007AFF;
	text-shadow: 0 0 10rpx rgba(0, 122, 255, 0.5);
	line-height: 1;
}

.feature-text {
	flex: 1;
}

.feature-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
	text-shadow: 0 0 10rpx rgba(0, 122, 255, 0.3);
}

.feature-desc {
	font-size: 24rpx;
	color: #b8c5d6;
	line-height: 1.4;
}

/* 开始按钮区域 */
.start-section {
	text-align: center;
}

.start-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100rpx;
	border-radius: 50rpx;
	margin-bottom: 20rpx;
	background: linear-gradient(135deg, #007AFF 0%, #6C5CE7 100%);
	box-shadow:
		0 20rpx 40rpx rgba(0, 122, 255, 0.3),
		0 0 60rpx rgba(0, 122, 255, 0.2),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
	border: 1rpx solid rgba(255, 255, 255, 0.1);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.start-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s ease;
}

.start-btn:active::before {
	left: 100%;
}

.start-btn:active {
	transform: translateY(-4rpx);
	box-shadow:
		0 25rpx 50rpx rgba(0, 122, 255, 0.4),
		0 0 80rpx rgba(0, 122, 255, 0.3);
}

.start-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #fff;
	margin-right: 15rpx;
	text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
	z-index: 1;
}

.start-arrow {
	font-size: 32rpx;
	color: #fff;
	text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
	z-index: 1;
	font-weight: bold;
	line-height: 1;
}

.start-tip {
	font-size: 24rpx;
	color: #8a9bb0;
}
</style>
