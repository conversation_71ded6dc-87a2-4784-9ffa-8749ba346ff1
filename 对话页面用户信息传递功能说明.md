# 对话页面用户信息传递功能说明

## 功能概述

实现了从 `/pagesB/dreamark/dialogue` 对话页面收集用户信息（姓名、年龄、梦想），并传递到 `/pagesB/dreamark/camera-new` 拍照页面的完整功能。

## 实现的功能

### 1. 用户信息收集
- **对话页面** (`dialogue.vue`) 收集用户的：
  - 姓名
  - 年龄  
  - 梦想

### 2. 数据传递
- 在对话完成后跳转到拍照页面时，通过URL参数传递用户信息
- 拍照页面接收参数并保存到本地存储

### 3. 自动填入功能
- **弹窗自动填入**：在拍照页面的配置弹窗中，"职业/梦想"输入框会自动填入对话页面收集的梦想内容
- **与未来对话优化**：在与未来对话功能中自动加入个人信息

### 4. AI分析内容优化
- 使用完整的个人信息构建AI分析的梦想内容
- 格式：`我叫[姓名]，我今年[年龄]岁，我的梦想是[梦想内容]`

## 技术实现

### dialogue.vue 修改

#### 1. goToCamera() 方法优化
```javascript
// 跳转到拍照页面
goToCamera() {
    // 构建URL参数，传递用户信息到下一个页面
    const params = new URLSearchParams();
    if (this.dialogueData.name) {
        params.append('name', this.dialogueData.name);
    }
    if (this.dialogueData.age) {
        params.append('age', this.dialogueData.age);
    }
    if (this.dialogueData.dream) {
        params.append('dream', this.dialogueData.dream);
    }
    
    const url = '/pagesB/dreamark/camera-new' + (params.toString() ? '?' + params.toString() : '');
    console.log('跳转到拍照页面，携带参数:', url);
    
    uni.navigateTo({
        url: url
    });
},
```

### camera-new.vue 修改

#### 1. onLoad() 方法优化
```javascript
onLoad(options) {
    console.log('=== 摄像头页面加载开始 ===');
    console.log('接收到的页面参数:', options);

    // 保存从对话页面传递过来的用户信息
    if (options.name) {
        uni.setStorageSync('user_dialogue_name', decodeURIComponent(options.name));
    }
    if (options.age) {
        uni.setStorageSync('user_dialogue_age', decodeURIComponent(options.age));
    }
    if (options.dream) {
        uni.setStorageSync('user_dialogue_dream', decodeURIComponent(options.dream));
    }
    
    // ... 其他初始化代码
},
```

#### 2. loadUserConfig() 方法优化
```javascript
loadUserConfig() {
    // 加载从对话页面传递过来的用户信息
    const dialogueName = uni.getStorageSync('user_dialogue_name');
    const dialogueAge = uni.getStorageSync('user_dialogue_age');
    const dialogueDream = uni.getStorageSync('user_dialogue_dream');

    // 优先使用对话页面的梦想信息，如果没有则使用已保存的职业信息
    if (dialogueDream && dialogueDream.trim() !== '') {
        this.userProfession = dialogueDream;
        console.log('自动填入对话页面的梦想:', dialogueDream);
    } else if (savedProfession) {
        this.userProfession = savedProfession;
    }
    
    // ... 其他配置加载代码
},
```

#### 3. startFutureChat() 方法优化
```javascript
startFutureChat() {
    // 获取用户对话信息
    const dialogueName = uni.getStorageSync('user_dialogue_name') || '';
    const dialogueAge = uni.getStorageSync('user_dialogue_age') || '';
    const dialogueDream = uni.getStorageSync('user_dialogue_dream') || '';

    // 构建用户信息参数
    const userInfo = {};
    if (dialogueName) {
        userInfo.name = dialogueName;
    }
    if (dialogueAge) {
        userInfo.age = dialogueAge;
    }
    if (dialogueDream) {
        userInfo.dream = dialogueDream;
    }

    // 调用API获取与未来对话的链接，传递用户信息
    app.post('ApiDreamInspiration/getFutureTalkUrl', userInfo, (res) => {
        // ... API处理逻辑
    });
},
```

#### 4. callAIAPI() 方法优化
```javascript
callAIAPI() {
    // 获取对话页面的用户信息
    const dialogueName = uni.getStorageSync('user_dialogue_name') || '';
    const dialogueAge = uni.getStorageSync('user_dialogue_age') || '';
    const dialogueDream = uni.getStorageSync('user_dialogue_dream') || '';

    // 构建梦想内容 - 优先使用对话页面的详细信息
    let dreamContent = '';
    if (dialogueName && dialogueAge && dialogueDream) {
        // 使用对话页面的完整信息
        dreamContent = `我叫${dialogueName}，我今年${dialogueAge}岁，我的梦想是${dialogueDream}。希望在未来能够实现自己的梦想，变得更加优秀和成功。`;
    } else {
        // 使用配置页面的基本信息
        dreamContent = `我是一个${userGender}，职业是${userProfession}，希望在未来能够实现自己的梦想，变得更加优秀和成功。`;
    }
    
    // ... API调用逻辑
},
```

## 数据流程

1. **用户在对话页面输入信息** → 保存到 `dialogueData` 对象
2. **对话完成跳转** → 通过URL参数传递到拍照页面
3. **拍照页面接收参数** → 保存到本地存储 (`user_dialogue_*`)
4. **配置弹窗显示** → 自动填入梦想内容到"职业/梦想"输入框
5. **与未来对话** → API请求包含完整的用户信息
6. **AI分析** → 使用个性化的梦想内容进行分析

## 存储键名

- `user_dialogue_name`: 用户姓名
- `user_dialogue_age`: 用户年龄  
- `user_dialogue_dream`: 用户梦想

## 用户体验提升

1. **无缝衔接**：用户在对话页面输入的信息自动传递到下一个页面
2. **自动填入**：减少用户重复输入，提升使用体验
3. **个性化内容**：AI分析和与未来对话都使用用户的真实信息
4. **信息完整性**：确保用户信息在整个流程中保持一致

## 注意事项

1. URL参数会自动进行编码/解码处理
2. 本地存储确保数据在页面间传递的可靠性
3. 优先级：对话页面信息 > 配置页面信息
4. 兼容性：如果没有对话信息，仍使用原有的配置方式

## 测试建议

1. 测试完整流程：对话页面 → 拍照页面 → 配置弹窗
2. 测试数据传递：确认姓名、年龄、梦想正确传递
3. 测试自动填入：验证弹窗中梦想内容自动填入
4. 测试API调用：确认与未来对话和AI分析使用正确的用户信息
